import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Participant } from '@/types/scoring';

interface ParticipantFormProps {
  onSubmit: (participant: Omit<Participant, 'id'>) => void;
  onCancel?: () => void;
  initialData?: Partial<Participant>;
}

export function ParticipantForm({ onSubmit, onCancel, initialData }: ParticipantFormProps) {
  const [formData, setFormData] = useState({
    name: initialData?.name || '',
    ringNumber: initialData?.ringNumber || '',
    owner: initialData?.owner || ''
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: { [key: string]: string } = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Participant name is required';
    }
    
    if (!formData.ringNumber.trim()) {
      newErrors.ringNumber = 'Ring number is required';
    }
    
    if (!formData.owner.trim()) {
      newErrors.owner = 'Owner name is required';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      onSubmit({
        name: formData.name.trim(),
        ringNumber: formData.ringNumber.trim(),
        owner: formData.owner.trim()
      });
      
      // Reset form
      setFormData({
        name: '',
        ringNumber: '',
        owner: ''
      });
    }
  };

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Participant Name *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => updateFormData('name', e.target.value)}
          placeholder="Enter participant name"
          className={errors.name ? 'border-destructive' : ''}
        />
        {errors.name && <p className="text-sm text-destructive mt-1">{errors.name}</p>}
      </div>

      <div>
        <Label htmlFor="ringNumber">Ring Number *</Label>
        <Input
          id="ringNumber"
          value={formData.ringNumber}
          onChange={(e) => updateFormData('ringNumber', e.target.value)}
          placeholder="Enter ring number"
          className={errors.ringNumber ? 'border-destructive' : ''}
        />
        {errors.ringNumber && <p className="text-sm text-destructive mt-1">{errors.ringNumber}</p>}
      </div>

      <div>
        <Label htmlFor="owner">Owner *</Label>
        <Input
          id="owner"
          value={formData.owner}
          onChange={(e) => updateFormData('owner', e.target.value)}
          placeholder="Enter owner name"
          className={errors.owner ? 'border-destructive' : ''}
        />
        {errors.owner && <p className="text-sm text-destructive mt-1">{errors.owner}</p>}
      </div>

      <div className="flex gap-3 pt-4">
        <Button type="submit" className="flex-1">
          Add Participant
        </Button>
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
            Cancel
          </Button>
        )}
      </div>
    </form>
  );
}