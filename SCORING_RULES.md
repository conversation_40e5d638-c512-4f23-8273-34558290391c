# Aturan Validasi Scoring Participant - PERKUTUT_KOMPETISI

## Overview
Sistem scoring untuk kompetisi perkutut memiliki aturan validasi khusus yang harus dipenuhi untuk memastikan konsistensi dan keadilan dalam penilaian.

## Kriteria Penilaian

### 1. <PERSON><PERSON>
- **<PERSON><PERSON> yang diizinkan**: 9, 8.75, 8.5
- **Deskripsi**: Penilaian kualitas suara bagian depan burung perkutut

### 2. <PERSON>ara Tengah  
- **<PERSON><PERSON> yang diizinkan**: 9, 8.75, 8.5
- **Deskripsi**: Penilaian kualitas suara bagian tengah burung perkutut

### 3. <PERSON><PERSON>jung
- **<PERSON><PERSON> yang di<PERSON>inkan**: 9, 8.75, 8.5
- **Deskripsi**: Penilaian kualitas suara bagian ujung burung perkutut

### 4. <PERSON><PERSON>
- **<PERSON><PERSON> yang di<PERSON>inkan**: 9, 8.75, 8.5, 8
- **Deskripsi**: Penilaian irama dan ritme suara burung perkutut

### 5. <PERSON><PERSON>
- **<PERSON><PERSON> yang di<PERSON>inkan**: 9, 8.75, 8.5, 8
- **<PERSON>kripsi**: Penilaian kualitas keseluruhan suara burung perkutut

## Aturan Validasi Utama

### Rule 1: Validasi Score Irama
**Aturan**: Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung.

**Implementasi**:
```typescript
const maxFrontSounds = Math.max(criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung);
if (criteria.irama > maxFrontSounds) {
  errors.push('Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung');
}
```

**Contoh**:
- ✅ **Valid**: Suara Depan: 9, Suara Tengah: 8.75, Suara Ujung: 8.5, Irama: 8.5
- ❌ **Invalid**: Suara Depan: 8.5, Suara Tengah: 8.5, Suara Ujung: 8.5, Irama: 8.75

### Rule 2: Validasi Score Mutu Suara
**Aturan**: Score Mutu Suara tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, Suara Ujung, dan Irama.

**Implementasi**:
```typescript
const maxOtherCriteria = Math.max(criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung, criteria.irama);
if (criteria.mutuSuara > maxOtherCriteria) {
  errors.push('Score Mutu Suara tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, Suara Ujung, dan Irama');
}
```

**Contoh**:
- ✅ **Valid**: Suara Depan: 9, Suara Tengah: 8.75, Suara Ujung: 8.5, Irama: 8.5, Mutu Suara: 8.5
- ❌ **Invalid**: Suara Depan: 8.5, Suara Tengah: 8.5, Suara Ujung: 8.5, Irama: 8, Mutu Suara: 8.75

## Implementasi dalam Kode

### File Utama yang Terlibat:

1. **`src/types/scoring.ts`**
   - Definisi interface dan konstanta nilai yang diizinkan
   - `ALLOWED_VALUES` untuk setiap kriteria

2. **`src/utils/scoring.ts`**
   - Fungsi `validateScoring()` - validasi utama
   - Fungsi `calculateTotal()` - perhitungan total score
   - Fungsi helper untuk formatting dan kategorisasi

3. **`src/components/scoring/ScoringForm.tsx`**
   - Form input scoring dengan validasi real-time
   - Fungsi `validateScoringForm()` untuk validasi form
   - UI feedback untuk error dan success states

4. **`src/hooks/useScoring.ts`**
   - Hook untuk manajemen state scoring
   - Integrasi dengan validasi saat submit

## Kategori Score

Berdasarkan total score, participant dikategorikan sebagai:

- **Perfect** (43.75+): Orange badge
- **Excellent** (43.5+): Green badge  
- **Very Good** (43.25+): Blue badge
- **Good** (43+): Red badge
- **Average** (<43): Default badge

## Validasi Real-time

Sistem melakukan validasi secara real-time saat user mengisi form:

1. **Validasi Nilai**: Memastikan nilai sesuai dengan `ALLOWED_VALUES`
2. **Validasi Aturan**: Mengecek Rule 1 dan Rule 2 secara dinamis
3. **Feedback Visual**: Menampilkan error message dan disable submit button
4. **Perhitungan Total**: Otomatis menghitung dan menampilkan total score

## Error Messages

Semua error message menggunakan bahasa Indonesia untuk konsistensi:

- "Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung"
- "Score Mutu Suara tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, Suara Ujung, dan Irama"
- "Nilai [Kriteria] harus [nilai yang diizinkan]"

## Testing Scenarios

### Test Case 1: Valid Scoring
```typescript
const validScoring = {
  suaraDepan: 9,
  suaraTengah: 8.75,
  suaraUjung: 8.5,
  irama: 8.5,
  mutuSuara: 8.5
};
// Expected: No errors, total = 43.25
```

### Test Case 2: Invalid Irama
```typescript
const invalidIrama = {
  suaraDepan: 8.5,
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 8.75, // Invalid: exceeds max front sounds
  mutuSuara: 8
};
// Expected: Error for Irama rule
```

### Test Case 3: Invalid Mutu Suara
```typescript
const invalidMutuSuara = {
  suaraDepan: 8.5,
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 8,
  mutuSuara: 8.75 // Invalid: exceeds all other criteria
};
// Expected: Error for Mutu Suara rule
```

## Kesimpulan

Aturan validasi ini memastikan bahwa:
1. Irama tidak dapat dinilai lebih tinggi dari kualitas suara dasar (depan, tengah, ujung)
2. Mutu Suara sebagai penilaian keseluruhan tidak dapat melebihi semua kriteria lainnya
3. Sistem memberikan feedback yang jelas dan konsisten kepada juri
4. Validasi dilakukan secara real-time untuk pengalaman user yang baik
