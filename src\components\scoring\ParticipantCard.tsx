import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Participant, Score } from '@/types/scoring';
import { formatScore, getScoreCategory } from '@/utils/scoring';
import { Edit, Eye } from 'lucide-react';

interface ParticipantCardProps {
  participant: Participant;
  scores: Score[];
  currentRound: number;
  onScore: () => void;
  onView: () => void;
}

export function ParticipantCard({ participant, scores, currentRound, onScore, onView }: ParticipantCardProps) {
  const currentRoundScore = scores.find(s => s.round === currentRound);
  const hasCurrentScore = !!currentRoundScore;
  
  const totalAverageScore = scores.length > 0 
    ? scores.reduce((sum, score) => sum + score.total, 0) / scores.length 
    : 0;

  const category = hasCurrentScore ? getScoreCategory(currentRoundScore.total) : 'average';

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-border/50 hover:border-border bg-card/80 backdrop-blur-sm">
      <CardContent className="p-5 lg:p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="min-w-0 flex-1">
            <h3 className="font-bold text-lg lg:text-xl text-foreground mb-1 truncate">{participant.name}</h3>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground font-medium">
                Ring: <span className="text-foreground">{participant.ringNumber}</span>
              </p>
              <p className="text-sm text-muted-foreground truncate">
                Owner: <span className="text-foreground">{participant.owner}</span>
              </p>
            </div>
          </div>
          
          {hasCurrentScore && (
            <Badge 
              variant={category === 'perfect' ? 'perfect' : 
                      category === 'excellent' ? 'excellent' :
                      category === 'very-good' ? 'very-good' :
                      category === 'good' ? 'good' : 'secondary'}
              className="text-xs lg:text-sm font-bold shadow-sm"
            >
              R{currentRound}: {formatScore(currentRoundScore.total)}
            </Badge>
          )}
        </div>

        {/* Round Progress */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Round Progress</span>
            <span className="text-xs text-muted-foreground">{scores.length}/4</span>
          </div>
          <div className="grid grid-cols-4 gap-2">
            {[1, 2, 3, 4].map(round => {
              const roundScore = scores.find(s => s.round === round);
              return (
                <div
                  key={round}
                  className={`h-3 rounded-md relative overflow-hidden transition-all duration-300 ${
                    roundScore 
                      ? getScoreCategory(roundScore.total) === 'perfect' 
                        ? 'bg-score-perfect shadow-sm' 
                        : getScoreCategory(roundScore.total) === 'excellent'
                        ? 'bg-score-excellent shadow-sm'
                        : getScoreCategory(roundScore.total) === 'very-good'
                        ? 'bg-score-very-good shadow-sm'
                        : getScoreCategory(roundScore.total) === 'good'
                        ? 'bg-score-good shadow-sm'
                        : 'bg-score-average shadow-sm'
                      : 'bg-muted/70 border border-dashed border-muted-foreground/30'
                  }`}
                  title={roundScore ? `Round ${round}: ${formatScore(roundScore.total)}` : `Round ${round}: Not scored`}
                >
                  {roundScore && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent to-white/20" />
                  )}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
                    <span className="text-[10px] font-bold text-foreground">R{round}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Statistics */}
        {scores.length > 0 && (
          <div className="bg-muted/30 rounded-lg p-3 mb-4 border border-border/30">
            <div className="grid grid-cols-2 gap-3 text-center">
              <div>
                <div className="text-xs text-muted-foreground uppercase tracking-wide mb-1">Average</div>
                <div className="font-bold text-lg text-foreground">{formatScore(totalAverageScore)}</div>
              </div>
              <div>
                <div className="text-xs text-muted-foreground uppercase tracking-wide mb-1">Best Score</div>
                <div className="font-bold text-lg text-foreground">
                  {formatScore(Math.max(...scores.map(s => s.total)))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            variant={hasCurrentScore ? "outline" : "scoring"}
            size="sm"
            onClick={onScore}
            className="flex-1 font-semibold transition-all duration-200 hover:scale-105"
          >
            <Edit className="h-4 w-4 mr-2" />
            {hasCurrentScore ? 'Edit Score' : 'Add Score'}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onView}
            disabled={scores.length === 0}
            className="px-3 hover:bg-muted/50 transition-colors"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}