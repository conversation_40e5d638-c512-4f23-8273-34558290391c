import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Plus, Users, Trash2, Upload, Download } from 'lucide-react';
import { Participant } from '@/types/scoring';
import { ParticipantForm } from './ParticipantForm';
import { BlockCard } from './BlockCard';

interface ParticipantBlockManagerProps {
  blocks: { [blockNumber: number]: Participant[] };
  onUpdateBlocks: (blocks: { [blockNumber: number]: Participant[] }) => void;
}

export function ParticipantBlockManager({ blocks, onUpdateBlocks }: ParticipantBlockManagerProps) {
  const [selectedBlock, setSelectedBlock] = useState<number | null>(null);
  const [isAddParticipantOpen, setIsAddParticipantOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('blocks');

  const MAX_PARTICIPANTS_PER_BLOCK = 42;

  const createNewBlock = () => {
    const blockNumbers = Object.keys(blocks).map(Number);
    const nextBlockNumber = blockNumbers.length > 0 ? Math.max(...blockNumbers) + 1 : 1;
    
    const updatedBlocks = {
      ...blocks,
      [nextBlockNumber]: []
    };
    onUpdateBlocks(updatedBlocks);
    setSelectedBlock(nextBlockNumber);
  };

  const addParticipant = (participant: Omit<Participant, 'id'>) => {
    if (selectedBlock === null) return;

    const newParticipant: Participant = {
      ...participant,
      id: `p-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };

    const currentBlockParticipants = blocks[selectedBlock] || [];
    
    if (currentBlockParticipants.length >= MAX_PARTICIPANTS_PER_BLOCK) {
      alert(`Block ${selectedBlock} is full! Maximum ${MAX_PARTICIPANTS_PER_BLOCK} participants per block.`);
      return;
    }

    const updatedBlocks = {
      ...blocks,
      [selectedBlock]: [...currentBlockParticipants, newParticipant]
    };

    onUpdateBlocks(updatedBlocks);
    setIsAddParticipantOpen(false);
  };

  const removeParticipant = (blockNumber: number, participantId: string) => {
    const updatedBlocks = {
      ...blocks,
      [blockNumber]: blocks[blockNumber].filter(p => p.id !== participantId)
    };
    onUpdateBlocks(updatedBlocks);
  };

  const deleteBlock = (blockNumber: number) => {
    const updatedBlocks = { ...blocks };
    delete updatedBlocks[blockNumber];
    onUpdateBlocks(updatedBlocks);
    
    if (selectedBlock === blockNumber) {
      setSelectedBlock(null);
    }
  };

  const getTotalParticipants = () => {
    return Object.values(blocks).flat().length;
  };

  const getBlockNumbers = () => {
    return Object.keys(blocks).map(Number).sort((a, b) => a - b);
  };

  const selectedBlockParticipants = selectedBlock ? (blocks[selectedBlock] || []) : [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-bold">Participant Block Management</h3>
          <p className="text-muted-foreground">Organize participants into blocks of {MAX_PARTICIPANTS_PER_BLOCK} each</p>
        </div>
        <div className="flex gap-2">
          <Badge variant="secondary">{Object.keys(blocks).length} blocks</Badge>
          <Badge variant="outline">{getTotalParticipants()} total participants</Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-2 w-64">
          <TabsTrigger value="blocks" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Blocks
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Bulk Import
          </TabsTrigger>
        </TabsList>

        <TabsContent value="blocks" className="space-y-6 mt-6">
          {Object.keys(blocks).length === 0 ? (
            <Card className="border-dashed border-2 border-muted-foreground/25">
              <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                <Users className="h-16 w-16 text-muted-foreground/50 mb-4" />
                <h3 className="text-xl font-semibold mb-2">No Blocks Created</h3>
                <p className="text-muted-foreground mb-6">Create your first block to start adding participants</p>
                <Button onClick={createNewBlock}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Block
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Block List */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold">Blocks</h4>
                  <Button size="sm" onClick={createNewBlock}>
                    <Plus className="h-4 w-4 mr-2" />
                    New Block
                  </Button>
                </div>
                
                <div className="space-y-3">
                  {getBlockNumbers().map(blockNumber => (
                    <BlockCard
                      key={blockNumber}
                      blockNumber={blockNumber}
                      participants={blocks[blockNumber]}
                      maxParticipants={MAX_PARTICIPANTS_PER_BLOCK}
                      isSelected={selectedBlock === blockNumber}
                      onSelect={() => setSelectedBlock(blockNumber)}
                      onDelete={() => deleteBlock(blockNumber)}
                    />
                  ))}
                </div>
              </div>

              {/* Selected Block Details */}
              <div className="space-y-4">
                {selectedBlock ? (
                  <>
                    <div className="flex items-center justify-between">
                      <h4 className="text-lg font-semibold">Block {selectedBlock} Participants</h4>
                      <Dialog open={isAddParticipantOpen} onOpenChange={setIsAddParticipantOpen}>
                        <DialogTrigger asChild>
                          <Button 
                            size="sm" 
                            disabled={selectedBlockParticipants.length >= MAX_PARTICIPANTS_PER_BLOCK}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Participant
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Add Participant to Block {selectedBlock}</DialogTitle>
                          </DialogHeader>
                          <ParticipantForm onSubmit={addParticipant} />
                        </DialogContent>
                      </Dialog>
                    </div>

                    {selectedBlockParticipants.length >= MAX_PARTICIPANTS_PER_BLOCK && (
                      <div className="flex items-center gap-2 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                        <AlertCircle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm text-yellow-700">
                          Block {selectedBlock} is full ({MAX_PARTICIPANTS_PER_BLOCK}/{MAX_PARTICIPANTS_PER_BLOCK} participants)
                        </span>
                      </div>
                    )}

                    <Card>
                      <CardContent className="p-4">
                        {selectedBlockParticipants.length === 0 ? (
                          <div className="text-center py-8 text-muted-foreground">
                            <Users className="h-12 w-12 mx-auto mb-3 opacity-50" />
                            <p>No participants in this block yet</p>
                          </div>
                        ) : (
                          <div className="space-y-3">
                            {selectedBlockParticipants.map((participant, index) => (
                              <div key={participant.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                                <div className="flex items-center gap-3">
                                  <Badge variant="outline" className="w-8 h-8 rounded-full flex items-center justify-center p-0">
                                    {index + 1}
                                  </Badge>
                                  <div>
                                    <div className="font-medium">{participant.name}</div>
                                    <div className="text-sm text-muted-foreground">
                                      Ring {participant.ringNumber} • {participant.owner}
                                    </div>
                                  </div>
                                </div>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => removeParticipant(selectedBlock, participant.id)}
                                  className="text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </>
                ) : (
                  <Card className="border-dashed border-2 border-muted-foreground/25">
                    <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                      <Users className="h-12 w-12 text-muted-foreground/50 mb-3" />
                      <p className="text-muted-foreground">Select a block to view and manage participants</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="bulk" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Import Participants</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8 text-muted-foreground">
                <Upload className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Bulk import functionality coming soon</p>
                <p className="text-sm">Import participants from CSV or Excel files</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}