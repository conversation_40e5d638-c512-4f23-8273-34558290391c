import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Plus, Users, Trophy, Settings, Calendar, MapPin } from 'lucide-react';
import { Competition, Participant } from '@/types/scoring';
import { ParticipantBlockManager } from '@/components/competition/ParticipantBlockManager';
import { CompetitionForm } from '@/components/competition/CompetitionForm';

interface CompetitionWithBlocks extends Competition {
  blocks: { [blockNumber: number]: Participant[] };
}

const CompetitionManagement = () => {
  const [competitions, setCompetitions] = useState<CompetitionWithBlocks[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<CompetitionWithBlocks | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const handleCreateCompetition = (competitionData: Omit<Competition, 'id' | 'participants'>) => {
    const newCompetition: CompetitionWithBlocks = {
      ...competitionData,
      id: `comp-${Date.now()}`,
      participants: [],
      blocks: {}
    };
    setCompetitions(prev => [...prev, newCompetition]);
    setIsCreateDialogOpen(false);
  };

  const handleSelectCompetition = (competition: CompetitionWithBlocks) => {
    setSelectedCompetition(competition);
  };

  const handleUpdateBlocks = (blocks: { [blockNumber: number]: Participant[] }) => {
    if (selectedCompetition) {
      const updatedCompetition = {
        ...selectedCompetition,
        blocks,
        participants: Object.values(blocks).flat()
      };
      setSelectedCompetition(updatedCompetition);
      setCompetitions(prev => 
        prev.map(comp => comp.id === selectedCompetition.id ? updatedCompetition : comp)
      );
    }
  };

  const getTotalParticipants = (competition: CompetitionWithBlocks) => {
    return Object.values(competition.blocks).flat().length;
  };

  const getTotalBlocks = (competition: CompetitionWithBlocks) => {
    return Object.keys(competition.blocks).length;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/30">
      <div className="container mx-auto px-4 py-6 lg:py-8 space-y-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground mb-2">Competition Management</h1>
            <p className="text-muted-foreground text-lg">Manage competitions and organize participants into blocks</p>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="lg" className="shadow-lg">
                <Plus className="h-5 w-5 mr-2" />
                New Competition
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Competition</DialogTitle>
              </DialogHeader>
              <CompetitionForm onSubmit={handleCreateCompetition} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Competition Selection */}
        {!selectedCompetition ? (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-foreground">Select Competition</h2>
            
            {competitions.length === 0 ? (
              <Card className="border-dashed border-2 border-muted-foreground/25">
                <CardContent className="flex flex-col items-center justify-center py-12 text-center">
                  <Trophy className="h-16 w-16 text-muted-foreground/50 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No Competitions Yet</h3>
                  <p className="text-muted-foreground mb-6">Create your first competition to get started</p>
                  <Button onClick={() => setIsCreateDialogOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Competition
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {competitions.map(competition => (
                  <Card 
                    key={competition.id} 
                    className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105"
                    onClick={() => handleSelectCompetition(competition)}
                  >
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Trophy className="h-5 w-5 text-primary" />
                        {competition.name}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        {competition.date.toLocaleDateString()}
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Block {competition.block}</span>
                          <Badge variant="secondary">{getTotalBlocks(competition)} blocks</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Participants</span>
                          <Badge variant="outline">{getTotalParticipants(competition)} total</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Rounds</span>
                          <Badge variant="outline">{competition.currentRound}/{competition.maxRounds}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        ) : (
          /* Competition Management Tabs */
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button 
                  variant="outline" 
                  onClick={() => setSelectedCompetition(null)}
                  className="h-10"
                >
                  ← Back
                </Button>
                <div>
                  <h2 className="text-2xl font-bold text-foreground">{selectedCompetition.name}</h2>
                  <p className="text-muted-foreground">Block {selectedCompetition.block} • {selectedCompetition.date.toLocaleDateString()}</p>
                </div>
              </div>
              <div className="flex gap-2">
                <Badge variant="secondary">{getTotalBlocks(selectedCompetition)} blocks</Badge>
                <Badge variant="outline">{getTotalParticipants(selectedCompetition)} participants</Badge>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3 w-full max-w-md h-12">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <Trophy className="h-4 w-4" />
                  Overview
                </TabsTrigger>
                <TabsTrigger value="blocks" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Blocks
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-6 mt-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Total Participants</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-primary">{getTotalParticipants(selectedCompetition)}</div>
                      <p className="text-sm text-muted-foreground mt-1">Across all blocks</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Active Blocks</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-primary">{getTotalBlocks(selectedCompetition)}</div>
                      <p className="text-sm text-muted-foreground mt-1">42 participants each</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">Competition Status</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-primary">{selectedCompetition.currentRound}/{selectedCompetition.maxRounds}</div>
                      <p className="text-sm text-muted-foreground mt-1">Rounds completed</p>
                    </CardContent>
                  </Card>
                </div>

                {getTotalParticipants(selectedCompetition) === 0 && (
                  <Card className="border-dashed border-2 border-muted-foreground/25">
                    <CardContent className="flex flex-col items-center justify-center py-8 text-center">
                      <AlertCircle className="h-12 w-12 text-muted-foreground/50 mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Participants Added</h3>
                      <p className="text-muted-foreground mb-4">Add participants to blocks to get started</p>
                      <Button onClick={() => setActiveTab('blocks')}>
                        <Users className="h-4 w-4 mr-2" />
                        Manage Blocks
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="blocks" className="mt-6">
                <ParticipantBlockManager
                  blocks={selectedCompetition.blocks}
                  onUpdateBlocks={handleUpdateBlocks}
                />
              </TabsContent>

              <TabsContent value="settings" className="space-y-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Competition Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Competition Name</Label>
                        <Input value={selectedCompetition.name} readOnly />
                      </div>
                      <div>
                        <Label>Block Number</Label>
                        <Input value={selectedCompetition.block} readOnly />
                      </div>
                      <div>
                        <Label>Max Rounds</Label>
                        <Input value={selectedCompetition.maxRounds} readOnly />
                      </div>
                      <div>
                        <Label>Current Round</Label>
                        <Input value={selectedCompetition.currentRound} readOnly />
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Competition settings are currently read-only. Future updates will allow editing these values.
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
};

export default CompetitionManagement;