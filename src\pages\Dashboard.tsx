import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Trophy,
  Users,
  Calendar,
  BarChart3,
  TrendingUp,
  Clock,
  Award,
  Target,
  Plus,
  Eye,
} from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data - in real app this would come from API
const dashboardStats = {
  totalCompetitions: 12,
  activeCompetitions: 3,
  totalParticipants: 156,
  completedRounds: 24,
  upcomingEvents: 2,
  averageScore: 42.8,
};

const recentCompetitions = [
  {
    id: '1',
    name: 'Piala Gubernur Jawa Barat 2024',
    date: '2024-07-15',
    status: 'active',
    participants: 45,
    currentRound: 2,
    maxRounds: 3,
    location: 'Bandung',
  },
  {
    id: '2',
    name: '<PERSON><PERSON>araan Regional Bogor',
    date: '2024-07-12',
    status: 'completed',
    participants: 32,
    currentRound: 3,
    maxRounds: 3,
    location: '<PERSON><PERSON>',
  },
  {
    id: '3',
    name: 'Tournament Bekasi Open',
    date: '2024-07-20',
    status: 'upcoming',
    participants: 28,
    currentRound: 0,
    maxRounds: 2,
    location: 'Bekasi',
  },
];

const quickActions = [
  {
    title: 'Start New Competition',
    description: 'Create and configure a new competition',
    icon: Plus,
    href: '/competitions/create',
    color: 'bg-blue-500',
  },
  {
    title: 'Add Participants',
    description: 'Register new participants',
    icon: Users,
    href: '/participants/add',
    color: 'bg-green-500',
  },
  {
    title: 'Live Scoring',
    description: 'Score active competitions',
    icon: Target,
    href: '/scoring',
    color: 'bg-orange-500',
  },
  {
    title: 'View Results',
    description: 'Check competition results',
    icon: Award,
    href: '/results',
    color: 'bg-purple-500',
  },
];

export default function Dashboard() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back! Here's what's happening with your competitions.
          </p>
        </div>
        <Button asChild>
          <Link to="/competitions/create">
            <Plus className="h-4 w-4 mr-2" />
            New Competition
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Competitions</CardTitle>
            <Trophy className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.totalCompetitions}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+2</span> from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Competitions</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.activeCompetitions}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Participants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.totalParticipants}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+12</span> new this week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Score</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.averageScore}</div>
            <p className="text-xs text-muted-foreground">
              <span className="text-green-600">+0.5</span> from last competition
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to manage your competitions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {quickActions.map((action) => (
              <Link
                key={action.title}
                to={action.href}
                className="group relative overflow-hidden rounded-lg border p-4 hover:shadow-md transition-all"
              >
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${action.color} text-white`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium group-hover:text-primary transition-colors">
                      {action.title}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {action.description}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Competitions */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Competitions</CardTitle>
          <CardDescription>
            Latest competition activities and status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentCompetitions.map((competition) => (
              <div
                key={competition.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-medium">{competition.name}</h3>
                    <Badge
                      variant={
                        competition.status === 'active'
                          ? 'default'
                          : competition.status === 'completed'
                          ? 'secondary'
                          : 'outline'
                      }
                    >
                      {competition.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {new Date(competition.date).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {competition.participants} participants
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      Round {competition.currentRound}/{competition.maxRounds}
                    </span>
                  </div>
                  {competition.status === 'active' && (
                    <div className="mt-2">
                      <Progress
                        value={(competition.currentRound / competition.maxRounds) * 100}
                        className="h-2"
                      />
                    </div>
                  )}
                </div>
                <Button variant="ghost" size="sm" asChild>
                  <Link to={`/competitions/${competition.id}`}>
                    <Eye className="h-4 w-4" />
                  </Link>
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
