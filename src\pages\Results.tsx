import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Trophy,
  Medal,
  Award,
  Download,
  Eye,
  BarChart3,
  TrendingUp,
} from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data
const competitions = [
  { id: '1', name: 'Piala Gubernur Jawa Barat 2024', status: 'completed' },
  { id: '2', name: 'Kejuaraan Regional Bogor', status: 'completed' },
  { id: '3', name: 'Tournament Bekasi Open', status: 'active' },
];

const results = [
  {
    rank: 1,
    participantId: '1',
    name: '<PERSON><PERSON>',
    ringNumber: 'P001',
    birdName: '<PERSON>aru<PERSON>',
    totalScore: 44.25,
    rounds: [
      { round: 1, score: 43.75 },
      { round: 2, score: 44.0 },
      { round: 3, score: 44.25 },
    ],
    category: 'Senior',
    prize: 'Champion',
  },
  {
    rank: 2,
    participantId: '2',
    name: 'Ahmad Wijaya',
    ringNumber: 'P002',
    birdName: 'Rajawali Putih',
    totalScore: 43.75,
    rounds: [
      { round: 1, score: 43.5 },
      { round: 2, score: 43.75 },
      { round: 3, score: 43.75 },
    ],
    category: 'Senior',
    prize: 'Runner-up',
  },
  {
    rank: 3,
    participantId: '3',
    name: 'Siti Nurhaliza',
    ringNumber: 'P003',
    birdName: 'Elang Jawa',
    totalScore: 43.5,
    rounds: [
      { round: 1, score: 43.25 },
      { round: 2, score: 43.5 },
      { round: 3, score: 43.5 },
    ],
    category: 'Junior',
    prize: 'Third Place',
  },
  {
    rank: 4,
    participantId: '4',
    name: 'Rudi Hermawan',
    ringNumber: 'P004',
    birdName: 'Merpati Emas',
    totalScore: 43.25,
    rounds: [
      { round: 1, score: 43.0 },
      { round: 2, score: 43.25 },
      { round: 3, score: 43.25 },
    ],
    category: 'Senior',
    prize: null,
  },
];

export default function Results() {
  const [selectedCompetition, setSelectedCompetition] = useState('1');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredResults = results.filter((result) => {
    const matchesCategory = selectedCategory === 'all' || result.category.toLowerCase() === selectedCategory;
    return matchesCategory;
  });

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Trophy className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Award className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="text-lg font-bold text-muted-foreground">{rank}</span>;
    }
  };

  const getRankBadge = (rank: number) => {
    switch (rank) {
      case 1:
        return <Badge className="bg-yellow-500 text-white">Champion</Badge>;
      case 2:
        return <Badge className="bg-gray-400 text-white">Runner-up</Badge>;
      case 3:
        return <Badge className="bg-amber-600 text-white">Third Place</Badge>;
      default:
        return <Badge variant="outline">#{rank}</Badge>;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 44) return 'text-green-600 font-bold';
    if (score >= 43.5) return 'text-blue-600 font-bold';
    if (score >= 43) return 'text-orange-600 font-bold';
    return 'text-muted-foreground';
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Competition Results</h1>
          <p className="text-muted-foreground">
            View rankings, scores, and competition outcomes
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Results
          </Button>
          <Button variant="outline">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Statistics
          </Button>
        </div>
      </div>

      {/* Competition Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Competition</CardTitle>
          <CardDescription>
            Choose a competition to view its results and rankings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Select value={selectedCompetition} onValueChange={setSelectedCompetition}>
                <SelectTrigger>
                  <SelectValue placeholder="Select competition" />
                </SelectTrigger>
                <SelectContent>
                  {competitions.map((competition) => (
                    <SelectItem key={competition.id} value={competition.id}>
                      <div className="flex items-center gap-2">
                        <span>{competition.name}</span>
                        <Badge variant={competition.status === 'completed' ? 'secondary' : 'default'}>
                          {competition.status}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('all')}
              >
                All Categories
              </Button>
              <Button
                variant={selectedCategory === 'senior' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('senior')}
              >
                Senior
              </Button>
              <Button
                variant={selectedCategory === 'junior' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('junior')}
              >
                Junior
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top 3 Podium */}
      <div className="grid gap-4 md:grid-cols-3">
        {filteredResults.slice(0, 3).map((result, index) => (
          <Card key={result.participantId} className={`${index === 0 ? 'ring-2 ring-yellow-500' : ''}`}>
            <CardHeader className="text-center pb-2">
              <div className="flex justify-center mb-2">
                {getRankIcon(result.rank)}
              </div>
              <CardTitle className="text-lg">{result.name}</CardTitle>
              <CardDescription>{result.birdName}</CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <div className={`text-2xl font-bold mb-2 ${getScoreColor(result.totalScore)}`}>
                {result.totalScore}
              </div>
              {getRankBadge(result.rank)}
              <div className="mt-3 text-sm text-muted-foreground">
                Ring: {result.ringNumber}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Full Results Table */}
      <Card>
        <CardHeader>
          <CardTitle>Complete Rankings</CardTitle>
          <CardDescription>
            Detailed results for all participants
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">Rank</TableHead>
                  <TableHead>Participant</TableHead>
                  <TableHead>Bird</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Round Scores</TableHead>
                  <TableHead>Total Score</TableHead>
                  <TableHead>Prize</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredResults.map((result) => (
                  <TableRow key={result.participantId}>
                    <TableCell>
                      <div className="flex items-center justify-center">
                        {getRankIcon(result.rank)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`/placeholder-avatar-${result.participantId}.jpg`} />
                          <AvatarFallback>
                            {result.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{result.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {result.ringNumber}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {result.birdName}
                    </TableCell>
                    <TableCell>
                      <Badge variant={result.category === 'Senior' ? 'default' : 'secondary'}>
                        {result.category}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {result.rounds.map((round) => (
                          <Badge key={round.round} variant="outline" className="text-xs">
                            R{round.round}: {round.score}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`text-lg font-bold ${getScoreColor(result.totalScore)}`}>
                        {result.totalScore}
                      </div>
                    </TableCell>
                    <TableCell>
                      {result.prize ? (
                        <Badge className="bg-gradient-to-r from-yellow-400 to-yellow-600 text-white">
                          {result.prize}
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="ghost" size="sm" asChild>
                        <Link to={`/participants/${result.participantId}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
