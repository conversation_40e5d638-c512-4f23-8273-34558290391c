import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  User,
  Phone,
  Mail,
  Award,
} from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data
const participants = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    ringNumber: 'P001',
    birdName: 'Garuda Emas',
    owner: '<PERSON><PERSON>',
    phone: '+62 812-3456-7890',
    email: '<EMAIL>',
    category: 'Senior',
    competitions: 5,
    bestScore: 44.25,
    status: 'active',
  },
  {
    id: '2',
    name: 'Ahmad Wijaya',
    ringNumber: 'P002',
    birdName: 'Rajawali Putih',
    owner: 'Ahmad Wijaya',
    phone: '+62 813-4567-8901',
    email: '<EMAIL>',
    category: 'Senior',
    competitions: 8,
    bestScore: 43.75,
    status: 'active',
  },
  {
    id: '3',
    name: 'Siti Nurhaliza',
    ringNumber: 'P003',
    birdName: 'Elang Jawa',
    owner: 'Siti Nurhaliza',
    phone: '+62 814-5678-9012',
    email: '<EMAIL>',
    category: 'Junior',
    competitions: 3,
    bestScore: 42.5,
    status: 'active',
  },
  {
    id: '4',
    name: 'Rudi Hermawan',
    ringNumber: 'P004',
    birdName: 'Merpati Emas',
    owner: 'Rudi Hermawan',
    phone: '+62 815-6789-0123',
    email: '<EMAIL>',
    category: 'Senior',
    competitions: 12,
    bestScore: 45.0,
    status: 'inactive',
  },
];

export default function Participants() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');

  const filteredParticipants = participants.filter((participant) => {
    const matchesSearch = 
      participant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      participant.ringNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      participant.birdName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      participant.owner.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || participant.category.toLowerCase() === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    return status === 'active' ? 'bg-green-500' : 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Participants</h1>
          <p className="text-muted-foreground">
            Manage participant registrations and information
          </p>
        </div>
        <Button asChild>
          <Link to="/participants/add">
            <Plus className="h-4 w-4 mr-2" />
            Add Participant
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Participants</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{participants.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <User className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {participants.filter(p => p.status === 'active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Senior Category</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {participants.filter(p => p.category === 'Senior').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Junior Category</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {participants.filter(p => p.category === 'Junior').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Participants Management */}
      <Card>
        <CardHeader>
          <CardTitle>Participant Management</CardTitle>
          <CardDescription>
            View, edit, and manage participant information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search participants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={categoryFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategoryFilter('all')}
              >
                All
              </Button>
              <Button
                variant={categoryFilter === 'senior' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategoryFilter('senior')}
              >
                Senior
              </Button>
              <Button
                variant={categoryFilter === 'junior' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategoryFilter('junior')}
              >
                Junior
              </Button>
            </div>
          </div>

          {/* Participants Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Participant</TableHead>
                  <TableHead>Ring Number</TableHead>
                  <TableHead>Bird Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Best Score</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredParticipants.map((participant) => (
                  <TableRow key={participant.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={`/placeholder-avatar-${participant.id}.jpg`} />
                          <AvatarFallback>
                            {participant.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{participant.name}</div>
                          <div className="text-sm text-muted-foreground">
                            Owner: {participant.owner}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{participant.ringNumber}</Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      {participant.birdName}
                    </TableCell>
                    <TableCell>
                      <Badge variant={participant.category === 'Senior' ? 'default' : 'secondary'}>
                        {participant.category}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-3 w-3" />
                          {participant.phone}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-3 w-3" />
                          {participant.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{participant.bestScore}</div>
                      <div className="text-sm text-muted-foreground">
                        {participant.competitions} competitions
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="secondary"
                        className={`${getStatusColor(participant.status)} text-white`}
                      >
                        {participant.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem asChild>
                            <Link to={`/participants/${participant.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Profile
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link to={`/participants/${participant.id}/edit`}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredParticipants.length === 0 && (
            <div className="text-center py-8">
              <p className="text-muted-foreground">No participants found.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
