import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeft,
  User,
  Bird,
  Phone,
  Mail,
  Award,
  Save,
  X,
  AlertCircle,
  CheckCircle,
} from 'lucide-react';
import { ExtendedParticipant } from '@/types/scoring';

interface FormData {
  name: string;
  ringNumber: string;
  birdName: string;
  owner: string;
  phone: string;
  email: string;
  category: 'Senior' | 'Junior' | '';
  address: string;
  notes: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function AddParticipant() {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    ringNumber: '',
    birdName: '',
    owner: '',
    phone: '',
    email: '',
    category: '',
    address: '',
    notes: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGeneratingRingNumber, setIsGeneratingRingNumber] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required fields validation
    if (!formData.name.trim()) {
      newErrors.name = 'Participant name is required';
    }

    if (!formData.ringNumber.trim()) {
      newErrors.ringNumber = 'Ring number is required';
    } else if (!/^[A-Z0-9]+$/.test(formData.ringNumber.trim())) {
      newErrors.ringNumber = 'Ring number should contain only letters and numbers';
    } else if (formData.ringNumber.trim().length < 3) {
      newErrors.ringNumber = 'Ring number should be at least 3 characters';
    }

    if (!formData.birdName.trim()) {
      newErrors.birdName = 'Bird name is required';
    }

    if (!formData.owner.trim()) {
      newErrors.owner = 'Owner name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else if (!/^(\+62|62|0)[0-9]{9,12}$/.test(formData.phone.trim())) {
      newErrors.phone = 'Please enter a valid Indonesian phone number';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email.trim())) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newParticipant: Omit<ExtendedParticipant, 'id'> = {
        name: formData.name.trim(),
        ringNumber: formData.ringNumber.trim().toUpperCase(),
        birdName: formData.birdName.trim(),
        owner: formData.owner.trim(),
        phone: formData.phone.trim(),
        email: formData.email.trim().toLowerCase(),
        category: formData.category as 'Senior' | 'Junior',
        competitions: 0,
        bestScore: 0,
        status: 'active',
      };

      // In real app, this would be an API call
      console.log('New participant:', newParticipant);

      toast({
        title: "Success!",
        description: `Participant ${formData.name} has been added successfully`,
        variant: "default",
      });

      // Navigate back to participants list
      navigate('/participants');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add participant. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Check if form has any data
    const hasData = Object.values(formData).some(value => value.trim() !== '');

    if (hasData) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this page?'
      );
      if (!confirmed) return;
    }

    navigate('/participants');
  };

  const generateRingNumber = async () => {
    setIsGeneratingRingNumber(true);

    try {
      // Simulate API call to get next available ring number
      await new Promise(resolve => setTimeout(resolve, 500));

      // In real app, this would fetch from API
      const nextNumber = Math.floor(Math.random() * 900) + 100; // Generate random 3-digit number
      const newRingNumber = `P${nextNumber.toString().padStart(3, '0')}`;

      handleInputChange('ringNumber', newRingNumber);

      toast({
        title: "Ring Number Generated",
        description: `Generated ring number: ${newRingNumber}`,
        variant: "default",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate ring number",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingRingNumber(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Add New Participant</h1>
          <p className="text-muted-foreground">
            Register a new participant for competitions
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Participant Information
              </CardTitle>
              <CardDescription>
                Enter the participant and bird details below
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Participant Details */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <User className="h-4 w-4" />
                    Participant Details
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="name">Participant Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter participant name"
                        className={errors.name ? 'border-destructive' : ''}
                      />
                      {errors.name && (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.name}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="owner">Owner Name *</Label>
                      <Input
                        id="owner"
                        value={formData.owner}
                        onChange={(e) => handleInputChange('owner', e.target.value)}
                        placeholder="Enter owner name"
                        className={errors.owner ? 'border-destructive' : ''}
                      />
                      {errors.owner && (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.owner}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+62 812-3456-7890"
                        className={errors.phone ? 'border-destructive' : ''}
                      />
                      {errors.phone && (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.phone}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                        className={errors.email ? 'border-destructive' : ''}
                      />
                      {errors.email && (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.email}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Bird Details */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Bird className="h-4 w-4" />
                    Bird Details
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="birdName">Bird Name *</Label>
                      <Input
                        id="birdName"
                        value={formData.birdName}
                        onChange={(e) => handleInputChange('birdName', e.target.value)}
                        placeholder="Enter bird name"
                        className={errors.birdName ? 'border-destructive' : ''}
                      />
                      {errors.birdName && (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.birdName}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="ringNumber">Ring Number *</Label>
                      <div className="flex gap-2">
                        <Input
                          id="ringNumber"
                          value={formData.ringNumber}
                          onChange={(e) => handleInputChange('ringNumber', e.target.value.toUpperCase())}
                          placeholder="P001"
                          className={errors.ringNumber ? 'border-destructive' : ''}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          onClick={generateRingNumber}
                          disabled={isGeneratingRingNumber}
                          className="shrink-0"
                        >
                          {isGeneratingRingNumber ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
                          ) : (
                            'Generate'
                          )}
                        </Button>
                      </div>
                      {errors.ringNumber && (
                        <p className="text-sm text-destructive flex items-center gap-1">
                          <AlertCircle className="h-3 w-3" />
                          {errors.ringNumber}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger className={errors.category ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Senior">Senior</SelectItem>
                        <SelectItem value="Junior">Junior</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.category && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.category}
                      </p>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Additional Information */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Award className="h-4 w-4" />
                    Additional Information
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="Enter address (optional)"
                      rows={3}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="notes">Notes</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="Any additional notes (optional)"
                      rows={3}
                    />
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex gap-3 pt-6">
                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Adding Participant...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Add Participant
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                    className="flex-1"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar Info */}
        <div className="space-y-6">
          {/* Preview Card */}
          {(formData.name || formData.birdName || formData.ringNumber) && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Preview</CardTitle>
                <CardDescription>
                  Preview of participant information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {formData.name && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Participant:</span>
                    <span className="text-sm font-medium">{formData.name}</span>
                  </div>
                )}
                {formData.birdName && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Bird:</span>
                    <span className="text-sm font-medium">{formData.birdName}</span>
                  </div>
                )}
                {formData.ringNumber && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Ring:</span>
                    <Badge variant="outline">{formData.ringNumber}</Badge>
                  </div>
                )}
                {formData.category && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Category:</span>
                    <Badge variant={formData.category === 'Senior' ? 'default' : 'secondary'}>
                      {formData.category}
                    </Badge>
                  </div>
                )}
                {formData.owner && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Owner:</span>
                    <span className="text-sm font-medium">{formData.owner}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Registration Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Required Information
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-6">
                  <li>• Participant name</li>
                  <li>• Owner name</li>
                  <li>• Phone number</li>
                  <li>• Email address</li>
                  <li>• Bird name</li>
                  <li>• Ring number</li>
                  <li>• Category (Senior/Junior)</li>
                </ul>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium">Categories</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="default">Senior</Badge>
                    <span className="text-sm text-muted-foreground">Experienced participants</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">Junior</Badge>
                    <span className="text-sm text-muted-foreground">New participants</span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium">Ring Number Format</h4>
                <p className="text-sm text-muted-foreground">
                  Use format like P001, P002, etc. Only letters and numbers allowed.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
