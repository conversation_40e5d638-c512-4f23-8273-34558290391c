@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 215 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 215 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 15%;

    --primary: 197 87% 25%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 40% 94%;
    --secondary-foreground: 215 25% 15%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 15% 45%;

    --accent: 158 64% 30%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 72% 51%;
    --destructive-foreground: 0 0% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 197 87% 25%;

    /* Competition scoring colors */
    --score-perfect: 25 95% 53%;         /* Orange for 43 3/4 */
    --score-perfect-foreground: 0 0% 98%;
    --score-excellent: 142 76% 36%;      /* <PERSON> for 43 1/2 */
    --score-excellent-foreground: 0 0% 98%;
    --score-very-good: 221 83% 53%;      /* Blue for 43 1/4 */
    --score-very-good-foreground: 0 0% 98%;
    --score-good: 0 84% 60%;             /* Red for 43 */
    --score-good-foreground: 0 0% 98%;
    --score-average: 220 14% 46%;
    --score-average-foreground: 0 0% 98%;

    /* Professional gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-scoring: linear-gradient(180deg, hsl(var(--background)), hsl(var(--muted)));

    /* Shadows for competition interface */
    --shadow-scoring: 0 4px 20px -4px hsl(var(--primary) / 0.15);
    --shadow-card: 0 2px 10px -2px hsl(0 0% 0% / 0.1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}