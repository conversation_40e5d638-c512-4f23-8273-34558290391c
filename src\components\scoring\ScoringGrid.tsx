import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Participant, Score, ScoringCriteria, ALLOWED_VALUES } from '@/types/scoring';
import { formatScore, getScoreCategory, calculateTotal } from '@/utils/scoring';
import { Edit } from 'lucide-react';

interface ScoringGridProps {
  participants: Participant[];
  scores: Score[];
  currentRound: number;
  onScore: (participant: Participant) => void;
  getParticipantScores: (participantId: string) => Score[];
  addScore: (participantId: string, criteria: ScoringCriteria) => void;
}

export function ScoringGrid({ participants, scores, currentRound, onScore, getParticipantScores, addScore }: ScoringGridProps) {
  const getScoreColor = (total: number): string => {
    const category = getScoreCategory(total);
    switch (category) {
      case 'perfect': return 'bg-orange-400'; // Orange for 43 3/4
      case 'excellent': return 'bg-green-400'; // Green for 43 1/2
      case 'very-good': return 'bg-blue-400'; // Blue for 43 1/4
      case 'good': return 'bg-red-400'; // Red for 43
      default: return 'bg-gray-200';
    }
  };

  const getParticipantCurrentScore = (participantId: string) => {
    return scores.find(s => s.participantId === participantId && s.round === currentRound);
  };

  const handleScoreChange = (participantId: string, criteriaKey: keyof ScoringCriteria, value: number) => {
    const currentScore = getParticipantCurrentScore(participantId);
    const currentCriteria = currentScore?.criteria || {
      suaraDepan: 0,
      suaraTengah: 0,
      suaraUjung: 0,
      irama: 0,
      mutuSuara: 0
    };

    const updatedCriteria = {
      ...currentCriteria,
      [criteriaKey]: value
    };

    // Always save the updated criteria (even partial)
    addScore(participantId, updatedCriteria);
  };

  const getCriteriaValue = (participantId: string, criteriaKey: keyof ScoringCriteria): number => {
    const currentScore = getParticipantCurrentScore(participantId);
    return currentScore?.criteria[criteriaKey] || 0;
  };

  const getTotalScore = (participantId: string): number => {
    const currentScore = getParticipantCurrentScore(participantId);
    if (!currentScore) return 0;

    const criteria = currentScore.criteria;
    const allFilled = Object.values(criteria).every(v => v > 0);

    return allFilled ? calculateTotal(criteria) : 0;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 border-b-2 border-gray-300 p-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-4 mb-2">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-bold">P3SI</span>
            </div>
            <div className="text-left">
              <h1 className="text-sm lg:text-lg font-bold">PERSATUAN PELESTARI PERKUTUT SELURUH INDONESIA</h1>
              <h2 className="text-base lg:text-xl font-bold">( P 3 S I )</h2>
              <h3 className="text-xs lg:text-base font-semibold">BIDANG KONKURS DAN PENJURIAN</h3>
              <h4 className="text-xs lg:text-sm font-medium">UNTUK JURI PENILAI</h4>
            </div>
          </div>
        </div>
      </div>

      {/* Scoring Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-100">
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">NO</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">Suara Depan</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">Suara Tengah</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">Suara Ujung</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">Irama</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">Mutu Suara</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">Jumlah</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">PARAF</th>
              <th className="border-2 border-gray-400 p-1 lg:p-2 text-xs lg:text-sm font-bold">KET</th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant, index) => {
              const currentScore = getParticipantCurrentScore(participant.id);
              const hasScore = !!currentScore;
              const totalScore = getTotalScore(participant.id);
              const hasCompleteScore = totalScore > 0;

              return (
                <tr key={participant.id} className="hover:bg-gray-50">
                  <td className="border border-gray-400 p-1 lg:p-2 text-center font-bold text-xs lg:text-sm">
                    {index + 1}
                  </td>

                  {/* Suara Depan */}
                  <td className="border border-gray-400 p-1 text-center">
                    <Select
                      value={getCriteriaValue(participant.id, 'suaraDepan') === 0 ? "" : getCriteriaValue(participant.id, 'suaraDepan').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'suaraDepan', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-8 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.suaraDepan.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Suara Tengah */}
                  <td className="border border-gray-400 p-1 text-center">
                    <Select
                      value={getCriteriaValue(participant.id, 'suaraTengah') === 0 ? "" : getCriteriaValue(participant.id, 'suaraTengah').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'suaraTengah', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-8 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.suaraTengah.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Suara Ujung */}
                  <td className="border border-gray-400 p-1 text-center">
                    <Select
                      value={getCriteriaValue(participant.id, 'suaraUjung') === 0 ? "" : getCriteriaValue(participant.id, 'suaraUjung').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'suaraUjung', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-8 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.suaraUjung.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Irama */}
                  <td className="border border-gray-400 p-1 text-center">
                    <Select
                      value={getCriteriaValue(participant.id, 'irama') === 0 ? "" : getCriteriaValue(participant.id, 'irama').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'irama', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-8 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.irama.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Mutu Suara */}
                  <td className="border border-gray-400 p-1 text-center">
                    <Select
                      value={getCriteriaValue(participant.id, 'mutuSuara') === 0 ? "" : getCriteriaValue(participant.id, 'mutuSuara').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'mutuSuara', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-8 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.mutuSuara.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Jumlah */}
                  <td className={`border border-gray-400 p-1 lg:p-2 text-center font-bold text-xs lg:text-sm ${
                    hasCompleteScore ? getScoreColor(totalScore) : ''
                  }`}>
                    {hasCompleteScore ? formatScore(totalScore) : ''}
                  </td>
                  
                  {/* Paraf */}
                  <td className="border border-gray-400 p-1 lg:p-2 text-center">
                    {hasCompleteScore && (
                      <div className="flex items-center justify-center gap-1">
                        <div className="w-4 h-4 lg:w-6 lg:h-6 bg-gray-400 rounded"></div>
                        {getScoreCategory(totalScore) === 'very-good' && (
                          <div className="w-4 h-4 lg:w-6 lg:h-6 bg-blue-400 rounded"></div>
                        )}
                        {getScoreCategory(totalScore) === 'good' && (
                          <div className="w-4 h-4 lg:w-6 lg:h-6 bg-red-400 rounded"></div>
                        )}
                      </div>
                    )}
                  </td>

                  {/* Keterangan */}
                  <td className="border border-gray-400 p-1 lg:p-2 text-center min-w-[100px]">
                    <div className="flex flex-col items-center gap-1">
                      <div className="text-xs font-medium truncate max-w-full">
                        {participant.name}
                      </div>
                      <div className="text-xs text-gray-600">
                        Ring: {participant.ringNumber}
                      </div>
                    </div>
                  </td>
                </tr>
              );
            })}
            
            {/* Empty rows to match the format */}
            {Array.from({ length: Math.max(0, 15 - participants.length) }, (_, i) => (
              <tr key={`empty-${i}`}>
                <td className="border border-gray-400 p-1 lg:p-2 text-center font-bold text-xs lg:text-sm">
                  {participants.length + i + 1}
                </td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
                <td className="border border-gray-400 p-1 lg:p-2 h-8 lg:h-10"></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer Info */}
      <div className="bg-gray-50 border-t-2 border-gray-300 p-4">
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <div className="font-bold mb-2">Nama Juri</div>
            <div className="border-b border-gray-400 h-8"></div>
          </div>
          <div>
            <div className="font-bold mb-2">KELAS</div>
            <div className="border-b border-gray-400 h-8"></div>
          </div>
          <div>
            <div className="font-bold mb-2">BABAK :</div>
            <div className="border-b border-gray-400 h-8 flex items-center">
              <Badge variant="secondary" className="ml-2">
                Round {currentRound}
              </Badge>
            </div>
          </div>
        </div>
        <div className="mt-4">
          <div className="font-bold mb-2">BLOK :</div>
          <div className="border-b border-gray-400 h-8"></div>
        </div>
      </div>

      {/* Color Legend */}
      <div className="bg-gray-100 p-4 border-t">
        <div className="text-sm font-bold mb-2">Score Categories:</div>
        <div className="flex flex-wrap gap-4 text-xs">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-orange-400 rounded"></div>
            <span>43 3/4 (Perfect)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-400 rounded"></div>
            <span>43 1/2 (Excellent)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-400 rounded"></div>
            <span>43 1/4 (Very Good)</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-red-400 rounded"></div>
            <span>43 (Good)</span>
          </div>
        </div>
      </div>
    </div>
  );
}
