import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Participant, Score, ScoringCriteria, ALLOWED_VALUES } from '@/types/scoring';
import { formatScore, getScoreCategory, calculateTotal } from '@/utils/scoring';
import { Edit } from 'lucide-react';

interface ScoringGridProps {
  participants: Participant[];
  scores: Score[];
  currentRound: number;
  onScore: (participant: Participant) => void;
  getParticipantScores: (participantId: string) => Score[];
  addScore: (participantId: string, criteria: ScoringCriteria) => void;
}

export function ScoringGrid({ participants, scores, currentRound, onScore, getParticipantScores, addScore }: ScoringGridProps) {
  const getScoreColor = (total: number): string => {
    const category = getScoreCategory(total);
    switch (category) {
      case 'perfect': return 'bg-orange-400'; // Orange for 43 3/4
      case 'excellent': return 'bg-green-400'; // Green for 43 1/2
      case 'very-good': return 'bg-blue-400'; // Blue for 43 1/4
      case 'good': return 'bg-red-400'; // Red for 43
      default: return 'bg-gray-200';
    }
  };

  const getParticipantCurrentScore = (participantId: string) => {
    return scores.find(s => s.participantId === participantId && s.round === currentRound);
  };

  const handleScoreChange = (participantId: string, criteriaKey: keyof ScoringCriteria, value: number) => {
    const currentScore = getParticipantCurrentScore(participantId);
    const currentCriteria = currentScore?.criteria || {
      suaraDepan: 0,
      suaraTengah: 0,
      suaraUjung: 0,
      irama: 0,
      mutuSuara: 0
    };

    const updatedCriteria = {
      ...currentCriteria,
      [criteriaKey]: value
    };

    // Always save the updated criteria (even partial)
    addScore(participantId, updatedCriteria);
  };

  const getCriteriaValue = (participantId: string, criteriaKey: keyof ScoringCriteria): number => {
    const currentScore = getParticipantCurrentScore(participantId);
    return currentScore?.criteria[criteriaKey] || 0;
  };

  const getTotalScore = (participantId: string): number => {
    const currentScore = getParticipantCurrentScore(participantId);
    if (!currentScore) return 0;

    const criteria = currentScore.criteria;
    const allFilled = Object.values(criteria).every(v => v > 0);

    return allFilled ? calculateTotal(criteria) : 0;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 border-b-2 border-gray-300 p-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-4 mb-2">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-bold">P3SI</span>
            </div>
            <div className="text-left">
              <h1 className="text-sm lg:text-lg font-bold">PERSATUAN PELESTARI PERKUTUT SELURUH INDONESIA</h1>
              <h2 className="text-base lg:text-xl font-bold">( P 3 S I )</h2>
              <h3 className="text-xs lg:text-base font-semibold">BIDANG KONKURS DAN PENJURIAN</h3>
              <h4 className="text-xs lg:text-sm font-medium">UNTUK JURI PENILAI</h4>
            </div>
          </div>
        </div>
      </div>

      {/* Scoring Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse table-fixed min-w-full">
          <thead>
            <tr className="bg-gray-100">
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-8">NO</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-16">Suara Depan</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-16">Suara Tengah</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-16">Suara Ujung</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-12">Irama</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-16">Mutu Suara</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-16">Jumlah</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold" colSpan={4}>PARAF</th>
              <th className="border-2 border-gray-400 p-1 text-xs font-bold w-24">KET</th>
            </tr>
          </thead>
          <tbody>
            {participants.map((participant, index) => {
              const currentScore = getParticipantCurrentScore(participant.id);
              const hasScore = !!currentScore;
              const totalScore = getTotalScore(participant.id);
              const hasCompleteScore = totalScore > 0;

              return (
                <tr key={participant.id} className="hover:bg-gray-50">
                  <td className="border border-gray-400 p-1 text-center font-bold text-xs w-8">
                    {index + 1}
                  </td>

                  {/* Suara Depan */}
                  <td className="border border-gray-400 p-1 text-center w-16">
                    <Select
                      value={getCriteriaValue(participant.id, 'suaraDepan') === 0 ? "" : getCriteriaValue(participant.id, 'suaraDepan').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'suaraDepan', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-7 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.suaraDepan.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Suara Tengah */}
                  <td className="border border-gray-400 p-1 text-center w-16">
                    <Select
                      value={getCriteriaValue(participant.id, 'suaraTengah') === 0 ? "" : getCriteriaValue(participant.id, 'suaraTengah').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'suaraTengah', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-7 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.suaraTengah.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Suara Ujung */}
                  <td className="border border-gray-400 p-1 text-center w-16">
                    <Select
                      value={getCriteriaValue(participant.id, 'suaraUjung') === 0 ? "" : getCriteriaValue(participant.id, 'suaraUjung').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'suaraUjung', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-7 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.suaraUjung.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Irama */}
                  <td className="border border-gray-400 p-1 text-center w-12">
                    <Select
                      value={getCriteriaValue(participant.id, 'irama') === 0 ? "" : getCriteriaValue(participant.id, 'irama').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'irama', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-7 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.irama.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Mutu Suara */}
                  <td className="border border-gray-400 p-1 text-center w-16">
                    <Select
                      value={getCriteriaValue(participant.id, 'mutuSuara') === 0 ? "" : getCriteriaValue(participant.id, 'mutuSuara').toString()}
                      onValueChange={(value) => handleScoreChange(participant.id, 'mutuSuara', parseFloat(value))}
                    >
                      <SelectTrigger className="w-full h-7 text-xs border-gray-300 bg-white hover:bg-gray-50 focus:ring-1 focus:ring-blue-500">
                        <SelectValue placeholder="" />
                      </SelectTrigger>
                      <SelectContent>
                        {ALLOWED_VALUES.mutuSuara.map(value => (
                          <SelectItem key={value} value={value.toString()}>
                            {formatScore(value)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </td>

                  {/* Jumlah */}
                  <td className={`border border-gray-400 p-1 text-center font-bold text-xs w-16 ${
                    hasCompleteScore ? getScoreColor(totalScore) : ''
                  }`}>
                    {hasCompleteScore ? formatScore(totalScore) : ''}
                  </td>

                  {/* Paraf Column 1 */}
                  <td className="border border-gray-400 p-1 text-center w-6">
                    {hasCompleteScore && (
                      <div className="w-4 h-4 bg-gray-400 rounded mx-auto"></div>
                    )}
                  </td>

                  {/* Paraf Column 2 */}
                  <td className="border border-gray-400 p-1 text-center w-6">
                    {hasCompleteScore && getScoreCategory(totalScore) === 'very-good' && (
                      <div className="w-4 h-4 bg-blue-400 rounded mx-auto"></div>
                    )}
                  </td>

                  {/* Paraf Column 3 */}
                  <td className="border border-gray-400 p-1 text-center w-6">
                    {hasCompleteScore && getScoreCategory(totalScore) === 'good' && (
                      <div className="w-4 h-4 bg-red-400 rounded mx-auto"></div>
                    )}
                  </td>

                  {/* Paraf Column 4 */}
                  <td className="border border-gray-400 p-1 text-center w-6">
                    {/* Reserved for future use */}
                  </td>

                  {/* Keterangan */}
                  <td className="border border-gray-400 p-1 text-left w-24">
                    <div className="text-xs">
                      <div className="font-medium truncate">
                        {participant.name}
                      </div>
                      <div className="text-gray-600 text-xs">
                        {participant.ringNumber}
                      </div>
                    </div>
                  </td>
                </tr>
              );
            })}
            
            {/* Empty rows to match the format */}
            {Array.from({ length: Math.max(0, 15 - participants.length) }, (_, i) => (
              <tr key={`empty-${i}`}>
                <td className="border border-gray-400 p-1 text-center font-bold text-xs w-8">
                  {participants.length + i + 1}
                </td>
                <td className="border border-gray-400 p-1 h-8 w-16"></td>
                <td className="border border-gray-400 p-1 h-8 w-16"></td>
                <td className="border border-gray-400 p-1 h-8 w-16"></td>
                <td className="border border-gray-400 p-1 h-8 w-12"></td>
                <td className="border border-gray-400 p-1 h-8 w-16"></td>
                <td className="border border-gray-400 p-1 h-8 w-16"></td>
                <td className="border border-gray-400 p-1 h-8 w-6"></td>
                <td className="border border-gray-400 p-1 h-8 w-6"></td>
                <td className="border border-gray-400 p-1 h-8 w-6"></td>
                <td className="border border-gray-400 p-1 h-8 w-6"></td>
                <td className="border border-gray-400 p-1 h-8 w-24"></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer Info */}
      <div className="bg-gray-50 border-t-2 border-gray-300 p-3">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-3">
            <div>
              <div className="font-bold mb-1 text-xs">Nama Juri</div>
              <div className="border-b border-gray-400 h-6 bg-white"></div>
            </div>
            <div>
              <div className="font-bold mb-1 text-xs">KELAS</div>
              <div className="border-b border-gray-400 h-6 bg-white"></div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <div className="font-bold mb-1 text-xs">BABAK :</div>
              <div className="border-b border-gray-400 h-6 bg-white flex items-center px-2">
                <span className="text-xs">Round {currentRound}</span>
              </div>
            </div>
            <div>
              <div className="font-bold mb-1 text-xs">BLOK :</div>
              <div className="border-b border-gray-400 h-6 bg-white"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Color Legend */}
      <div className="bg-gray-100 p-2 border-t">
        <div className="text-xs font-bold mb-1">Score Categories:</div>
        <div className="flex flex-wrap gap-3 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-orange-400 rounded"></div>
            <span>43 3/4</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-green-400 rounded"></div>
            <span>43 1/2</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-blue-400 rounded"></div>
            <span>43 1/4</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-3 h-3 bg-red-400 rounded"></div>
            <span>43</span>
          </div>
        </div>
      </div>
    </div>
  );
}
