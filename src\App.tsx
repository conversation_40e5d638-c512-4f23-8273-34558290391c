import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { DashboardLayout } from "./components/layout/DashboardLayout";
import Landing from "./pages/Landing";
import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import Competitions from "./pages/Competitions";
import Participants from "./pages/Participants";
import Results from "./pages/Results";
import CompetitionManagement from "./pages/CompetitionManagement";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          {/* Landing page */}
          <Route path="/" element={<Landing />} />

          {/* Legacy scoring interface */}
          <Route path="/legacy-scoring" element={<Index />} />
          <Route path="/competition-management" element={<CompetitionManagement />} />

          {/* Dashboard routes with sidebar layout */}
          <Route path="/dashboard" element={<DashboardLayout><Dashboard /></DashboardLayout>} />
          <Route path="/competitions" element={<DashboardLayout><Competitions /></DashboardLayout>} />
          <Route path="/competitions/create" element={<DashboardLayout><div>Create Competition Page</div></DashboardLayout>} />
          <Route path="/competitions/:id" element={<DashboardLayout><div>Competition Details Page</div></DashboardLayout>} />
          <Route path="/competitions/:id/edit" element={<DashboardLayout><div>Edit Competition Page</div></DashboardLayout>} />
          <Route path="/participants" element={<DashboardLayout><Participants /></DashboardLayout>} />
          <Route path="/participants/add" element={<DashboardLayout><div>Add Participant Page</div></DashboardLayout>} />
          <Route path="/participants/:id" element={<DashboardLayout><div>Participant Details Page</div></DashboardLayout>} />
          <Route path="/participants/:id/edit" element={<DashboardLayout><div>Edit Participant Page</div></DashboardLayout>} />
          <Route path="/scoring" element={<DashboardLayout><Index /></DashboardLayout>} />
          <Route path="/scoring/review" element={<DashboardLayout><div>Score Review Page</div></DashboardLayout>} />
          <Route path="/scoring/history" element={<DashboardLayout><div>Score History Page</div></DashboardLayout>} />
          <Route path="/results" element={<DashboardLayout><Results /></DashboardLayout>} />
          <Route path="/results/rankings" element={<DashboardLayout><div>Rankings Page</div></DashboardLayout>} />
          <Route path="/results/statistics" element={<DashboardLayout><div>Statistics Page</div></DashboardLayout>} />
          <Route path="/reports" element={<DashboardLayout><div>Reports Page</div></DashboardLayout>} />
          <Route path="/settings" element={<DashboardLayout><div>Settings Page</div></DashboardLayout>} />

          {/* Catch-all route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
