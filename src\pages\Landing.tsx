import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Trophy, Users, BarChart3, Award, ArrowRight, Star } from 'lucide-react';
import { Link } from 'react-router-dom';

const features = [
  {
    icon: Trophy,
    title: 'Competition Management',
    description: 'Create and manage bird competitions with ease. Set up tournaments, configure rules, and track progress.',
  },
  {
    icon: Users,
    title: 'Participant Registration',
    description: 'Register participants, manage bird information, and organize categories for fair competition.',
  },
  {
    icon: BarChart3,
    title: 'Live Scoring System',
    description: 'Real-time scoring with P3SI standards validation. Ensure accurate and fair judging.',
  },
  {
    icon: Award,
    title: 'Results & Rankings',
    description: 'Generate comprehensive results, rankings, and export official competition reports.',
  },
];

const stats = [
  { label: 'Competitions Managed', value: '500+' },
  { label: 'Participants Registered', value: '10,000+' },
  { label: 'Judges Certified', value: '200+' },
  { label: 'Years of Experience', value: '5+' },
];

export default function Landing() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/30">
      {/* Header */}
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Trophy className="h-6 w-6" />
              </div>
              <div>
                <h1 className="text-xl font-bold">Perkutut Competition System</h1>
                <p className="text-sm text-muted-foreground">P3SI Certified Platform</p>
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" asChild>
                <Link to="/legacy-scoring">Legacy Scoring</Link>
              </Button>
              <Button asChild>
                <Link to="/dashboard">
                  Dashboard
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 lg:py-24">
        <div className="text-center max-w-4xl mx-auto">
          <div className="flex justify-center mb-6">
            <div className="flex items-center gap-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
              <Star className="h-4 w-4" />
              P3SI Certified Competition Platform
            </div>
          </div>
          <h1 className="text-4xl lg:text-6xl font-bold tracking-tight mb-6">
            Professional Bird Competition
            <span className="text-primary block">Management System</span>
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Streamline your perkutut competitions with our comprehensive platform. 
            From registration to results, manage everything with P3SI standard compliance.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link to="/dashboard">
                Get Started
                <ArrowRight className="h-5 w-5 ml-2" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link to="/competitions">
                View Competitions
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-primary mb-2">
                {stat.value}
              </div>
              <div className="text-muted-foreground">
                {stat.label}
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Everything You Need for Professional Competitions
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Our platform provides all the tools necessary to run successful bird competitions 
            according to P3SI standards.
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-16">
        <Card className="bg-gradient-to-r from-primary/10 to-primary/5 border-primary/20">
          <CardContent className="text-center py-16">
            <h2 className="text-3xl font-bold mb-4">
              Ready to Manage Your Next Competition?
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join hundreds of competition organizers who trust our platform 
              for their bird competitions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" asChild>
                <Link to="/dashboard">
                  Access Dashboard
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link to="/competitions/create">
                  Create Competition
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t bg-muted/30">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-muted-foreground">
            <p>&copy; 2024 Perkutut Competition System. Built with P3SI standards compliance.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
