import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Competition, Judge } from '@/types/scoring';
import { Trophy, Users, Calendar, Hash } from 'lucide-react';

interface CompetitionHeaderProps {
  competition: Competition;
  judge: Judge;
  onRoundChange: (round: number) => void;
}

export function CompetitionHeader({ competition, judge, onRoundChange }: CompetitionHeaderProps) {
  return (
    <Card className="bg-gradient-primary text-primary-foreground shadow-scoring">
      <CardContent className="p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          {/* Competition Info */}
          <div>
            <h1 className="text-2xl lg:text-3xl font-bold mb-2">{competition.name}</h1>
            <div className="flex flex-wrap gap-4 text-sm opacity-90">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {competition.date.toLocaleDateString('id-ID')}
              </div>
              <div className="flex items-center gap-1">
                <Hash className="h-4 w-4" />
                Block {competition.block}
              </div>
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {competition.participants.length} Participants
              </div>
            </div>
          </div>

          {/* Judge Info */}
          <div className="text-center lg:text-right">
            <div className="text-sm opacity-90 mb-1">Judge</div>
            <div className="font-semibold">{judge.name}</div>
            <div className="text-xs opacity-75">({judge.initials})</div>
          </div>
        </div>

        {/* Round Selector */}
        <div className="mt-6 pt-4 border-t border-primary-foreground/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              <span className="font-medium">Competition Rounds</span>
            </div>
            
            <div className="flex gap-2">
              {Array.from({ length: competition.maxRounds }, (_, i) => i + 1).map(round => (
                <Button
                  key={round}
                  variant={round === competition.currentRound ? "secondary" : "outline"}
                  size="sm"
                  onClick={() => onRoundChange(round)}
                  className={round === competition.currentRound 
                    ? "bg-primary-foreground text-primary" 
                    : "border-primary-foreground/30 text-primary-foreground hover:bg-primary-foreground/10"
                  }
                >
                  Babak {round}
                </Button>
              ))}
            </div>
          </div>
          
          <div className="mt-3">
            <Badge variant="secondary" className="bg-primary-foreground text-primary">
              Currently Judging: Round {competition.currentRound}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}