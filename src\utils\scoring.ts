import { ScoringCriteria, ALLOWED_VALUES } from '@/types/scoring';

export function validateScoring(criteria: ScoringCriteria): string[] {
  const errors: string[] = [];
  
  // Check if values are from allowed sets
  if (!(ALLOWED_VALUES.suaraDepan as readonly number[]).includes(criteria.suaraDepan)) {
    errors.push('Suara Depan must be 9, 8.75, or 8.5');
  }
  
  if (!(ALLOWED_VALUES.suaraTengah as readonly number[]).includes(criteria.suaraTengah)) {
    errors.push('Suara Tengah must be 9, 8.75, or 8.5');
  }
  
  if (!(ALLOWED_VALUES.suaraUjung as readonly number[]).includes(criteria.suaraUjung)) {
    errors.push('Suara Ujung must be 8.75 or 8.5');
  }
  
  if (!(ALLOWED_VALUES.irama as readonly number[]).includes(criteria.irama)) {
    errors.push('<PERSON><PERSON> must be 8.75, 8.5, or 8');
  }
  
  if (!(ALLOWED_VALUES.mutuSuara as readonly number[]).includes(criteria.mutuSuara)) {
    errors.push('<PERSON><PERSON> must be 8.75, 8.5, or 8');
  }
  
  // Rule 1: Score Irama tidak boleh melebihi nilai dari suara depan, tengah, dan ujung
  // Artinya: SEMUA kriteria suara (depan, tengah, ujung) harus memiliki nilai >= score irama
  if (criteria.irama > criteria.suaraDepan) {
    errors.push('Score Irama tidak boleh melebihi score Suara Depan');
  }
  if (criteria.irama > criteria.suaraTengah) {
    errors.push('Score Irama tidak boleh melebihi score Suara Tengah');
  }
  if (criteria.irama > criteria.suaraUjung) {
    errors.push('Score Irama tidak boleh melebihi score Suara Ujung');
  }

  // Rule 2: Score Mutu Suara tidak boleh melebihi nilai dari suara depan, tengah, ujung, dan irama
  // Artinya: SEMUA kriteria lain harus memiliki nilai >= score mutu suara
  if (criteria.mutuSuara > criteria.suaraDepan) {
    errors.push('Score Mutu Suara tidak boleh melebihi score Suara Depan');
  }
  if (criteria.mutuSuara > criteria.suaraTengah) {
    errors.push('Score Mutu Suara tidak boleh melebihi score Suara Tengah');
  }
  if (criteria.mutuSuara > criteria.suaraUjung) {
    errors.push('Score Mutu Suara tidak boleh melebihi score Suara Ujung');
  }
  if (criteria.mutuSuara > criteria.irama) {
    errors.push('Score Mutu Suara tidak boleh melebihi score Irama');
  }
  
  return errors;
}

export function calculateTotal(criteria: ScoringCriteria): number {
  return criteria.suaraDepan + criteria.suaraTengah + criteria.suaraUjung + criteria.irama + criteria.mutuSuara;
}

export function formatScore(score: number): string {
  if (score % 1 === 0) return score.toString();
  if (score % 1 === 0.25) return `${Math.floor(score)} 1/4`;
  if (score % 1 === 0.5) return `${Math.floor(score)} 1/2`;
  if (score % 1 === 0.75) return `${Math.floor(score)} 3/4`;
  return score.toString();
}

export function getScoreCategory(total: number): 'perfect' | 'excellent' | 'very-good' | 'good' | 'average' {
  if (total >= 43.75) return 'perfect';  // 43 3/4 - Orange
  if (total >= 43.5) return 'excellent'; // 43 1/2 - Green
  if (total >= 43.25) return 'very-good'; // 43 1/4 - Blue
  if (total >= 43) return 'good';        // 43 - Red
  return 'average';
}

export function getScoreColor(total: number): string {
  const category = getScoreCategory(total);
  switch (category) {
    case 'perfect': return 'score-perfect';    // Orange
    case 'excellent': return 'score-excellent'; // Green
    case 'very-good': return 'score-very-good'; // Blue
    case 'good': return 'score-good';          // Red
    case 'average': return 'score-average';
    default: return 'muted';
  }
}