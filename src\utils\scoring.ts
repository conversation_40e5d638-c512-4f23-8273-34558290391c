import { ScoringCriteria, ALLOWED_VALUES } from '@/types/scoring';

export function validateScoring(criteria: ScoringCriteria): string[] {
  const errors: string[] = [];
  
  // Check if values are from allowed sets
  if (!(ALLOWED_VALUES.suaraDepan as readonly number[]).includes(criteria.suaraDepan)) {
    errors.push('Suara Depan must be 9, 8.75, or 8.5');
  }
  
  if (!(ALLOWED_VALUES.suaraTengah as readonly number[]).includes(criteria.suaraTengah)) {
    errors.push('Suara Tengah must be 9, 8.75, or 8.5');
  }
  
  if (!(ALLOWED_VALUES.suaraUjung as readonly number[]).includes(criteria.suaraUjung)) {
    errors.push('Suara Ujung must be 8.75 or 8.5');
  }
  
  if (!(ALLOWED_VALUES.irama as readonly number[]).includes(criteria.irama)) {
    errors.push('<PERSON><PERSON> must be 8.75, 8.5, or 8');
  }
  
  if (!(ALLOWED_VALUES.mutuSuara as readonly number[]).includes(criteria.mutuSuara)) {
    errors.push('<PERSON><PERSON> must be 8.75, 8.5, or 8');
  }
  
  // Rule 1: Score Irama tidak boleh melebihi salah satu score dari ketiga kriteria (suara depan, tengah, ujung)
  // Artinya: setidaknya ada satu dari ketiga kriteria yang nilainya >= score irama
  const frontSounds = [criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung];
  const hasValidFrontSound = frontSounds.some(score => score >= criteria.irama);
  if (!hasValidFrontSound) {
    errors.push('Score Irama tidak boleh melebihi semua score Suara Depan, Suara Tengah, dan Suara Ujung. Setidaknya satu dari ketiga kriteria harus memiliki nilai >= score Irama');
  }

  // Rule 2: Score Mutu Suara tidak boleh melebihi salah satu score dari keempat kriteria (suara depan, tengah, ujung, irama)
  // Artinya: setidaknya ada satu dari keempat kriteria yang nilainya >= score mutu suara
  const allOtherCriteria = [criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung, criteria.irama];
  const hasValidOtherCriteria = allOtherCriteria.some(score => score >= criteria.mutuSuara);
  if (!hasValidOtherCriteria) {
    errors.push('Score Mutu Suara tidak boleh melebihi semua score Suara Depan, Suara Tengah, Suara Ujung, dan Irama. Setidaknya satu dari keempat kriteria harus memiliki nilai >= score Mutu Suara');
  }
  
  return errors;
}

export function calculateTotal(criteria: ScoringCriteria): number {
  return criteria.suaraDepan + criteria.suaraTengah + criteria.suaraUjung + criteria.irama + criteria.mutuSuara;
}

export function formatScore(score: number): string {
  if (score % 1 === 0) return score.toString();
  if (score % 1 === 0.25) return `${Math.floor(score)} 1/4`;
  if (score % 1 === 0.5) return `${Math.floor(score)} 1/2`;
  if (score % 1 === 0.75) return `${Math.floor(score)} 3/4`;
  return score.toString();
}

export function getScoreCategory(total: number): 'perfect' | 'excellent' | 'very-good' | 'good' | 'average' {
  if (total >= 43.75) return 'perfect';  // 43 3/4 - Orange
  if (total >= 43.5) return 'excellent'; // 43 1/2 - Green
  if (total >= 43.25) return 'very-good'; // 43 1/4 - Blue
  if (total >= 43) return 'good';        // 43 - Red
  return 'average';
}

export function getScoreColor(total: number): string {
  const category = getScoreCategory(total);
  switch (category) {
    case 'perfect': return 'score-perfect';    // Orange
    case 'excellent': return 'score-excellent'; // Green
    case 'very-good': return 'score-very-good'; // Blue
    case 'good': return 'score-good';          // Red
    case 'average': return 'score-average';
    default: return 'muted';
  }
}