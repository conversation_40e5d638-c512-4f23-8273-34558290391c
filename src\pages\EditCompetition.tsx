import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeft,
  Trophy,
  Calendar as CalendarIcon,
  MapPin,
  Users,
  DollarSign,
  Save,
  X,
  AlertCircle,
  Plus,
  Trash2,
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { ExtendedCompetition, Prize, ContactInfo } from '@/types/scoring';

// Mock data - in real app this would come from API
const mockCompetition: ExtendedCompetition = {
  id: '1',
  name: 'Piala Gubernur Jawa Barat 2024',
  description: 'Kompetisi perkutut tingkat regional untuk wilayah Jawa Barat dengan standar P3SI.',
  date: new Date('2024-07-15'),
  endDate: new Date('2024-07-16'),
  location: 'Bandung',
  venue: 'Gedung Serbaguna Bandung',
  organizer: 'P3SI Jawa Barat',
  category: 'Regional',
  status: 'active',
  maxParticipants: 100,
  registrationDeadline: new Date('2024-07-10'),
  entryFee: 150000,
  currency: 'IDR',
  currentRound: 2,
  maxRounds: 3,
  judges: [],
  participants: [],
  rules: 'Kompetisi menggunakan standar P3SI dengan 5 kriteria penilaian: Suara Depan, Suara Tengah, Suara Ujung, Irama, dan Mutu Suara.',
  prizes: [
    { id: '1', position: 1, title: 'Champion', amount: 5000000, currency: 'IDR' },
    { id: '2', position: 2, title: 'Runner-up', amount: 3000000, currency: 'IDR' },
    { id: '3', position: 3, title: 'Third Place', amount: 2000000, currency: 'IDR' },
  ],
  contactInfo: {
    name: 'Ahmad Suryana',
    phone: '+62 812-1234-5678',
    email: '<EMAIL>',
    website: 'https://p3si-jabar.org',
  },
  createdAt: new Date('2024-06-01'),
  updatedAt: new Date('2024-07-01'),
  createdBy: 'admin',
};

interface FormData {
  name: string;
  description: string;
  date: Date;
  endDate: Date | null;
  location: string;
  venue: string;
  organizer: string;
  category: 'Regional' | 'National' | 'Open' | 'Championship';
  maxParticipants: number;
  registrationDeadline: Date;
  entryFee: number;
  currency: string;
  maxRounds: number;
  rules: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  contactWebsite: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function EditCompetition() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [competition, setCompetition] = useState<ExtendedCompetition | null>(null);
  const [formData, setFormData] = useState<FormData | null>(null);
  const [prizes, setPrizes] = useState<Prize[]>([]);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isDateOpen, setIsDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);
  const [isRegDeadlineOpen, setIsRegDeadlineOpen] = useState(false);

  useEffect(() => {
    const fetchCompetition = async () => {
      setIsLoading(true);
      try {
        // In real app: const response = await api.getCompetition(id);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        setCompetition(mockCompetition);
        
        // Initialize form data
        setFormData({
          name: mockCompetition.name,
          description: mockCompetition.description,
          date: mockCompetition.date,
          endDate: mockCompetition.endDate || null,
          location: mockCompetition.location,
          venue: mockCompetition.venue,
          organizer: mockCompetition.organizer,
          category: mockCompetition.category,
          maxParticipants: mockCompetition.maxParticipants,
          registrationDeadline: mockCompetition.registrationDeadline,
          entryFee: mockCompetition.entryFee,
          currency: mockCompetition.currency,
          maxRounds: mockCompetition.maxRounds,
          rules: mockCompetition.rules,
          contactName: mockCompetition.contactInfo.name,
          contactPhone: mockCompetition.contactInfo.phone,
          contactEmail: mockCompetition.contactInfo.email,
          contactWebsite: mockCompetition.contactInfo.website || '',
        });
        
        setPrizes(mockCompetition.prizes);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load competition data",
          variant: "destructive",
        });
        navigate('/competitions');
      } finally {
        setIsLoading(false);
      }
    };

    fetchCompetition();
  }, [id, navigate, toast]);

  const validateForm = (): boolean => {
    if (!formData) return false;
    
    const newErrors: FormErrors = {};

    // Required fields validation
    if (!formData.name.trim()) {
      newErrors.name = 'Competition name is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (!formData.venue.trim()) {
      newErrors.venue = 'Venue is required';
    }

    if (!formData.organizer.trim()) {
      newErrors.organizer = 'Organizer is required';
    }

    if (formData.maxParticipants < 1) {
      newErrors.maxParticipants = 'Max participants must be at least 1';
    }

    if (formData.maxRounds < 1 || formData.maxRounds > 10) {
      newErrors.maxRounds = 'Max rounds must be between 1 and 10';
    }

    if (formData.entryFee < 0) {
      newErrors.entryFee = 'Entry fee cannot be negative';
    }

    if (!formData.contactName.trim()) {
      newErrors.contactName = 'Contact name is required';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Contact phone is required';
    } else if (!/^(\+62|62|0)[0-9]{9,12}$/.test(formData.contactPhone.trim())) {
      newErrors.contactPhone = 'Please enter a valid Indonesian phone number';
    }

    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = 'Contact email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail.trim())) {
      newErrors.contactEmail = 'Please enter a valid email address';
    }

    // Date validations
    if (formData.registrationDeadline >= formData.date) {
      newErrors.registrationDeadline = 'Registration deadline must be before competition date';
    }

    if (formData.endDate && formData.endDate < formData.date) {
      newErrors.endDate = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    if (!formData) return;
    
    setFormData(prev => prev ? { ...prev, [field]: value } : null);
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePrizeChange = (id: string, field: keyof Prize, value: any) => {
    setPrizes(prev => prev.map(prize => 
      prize.id === id ? { ...prize, [field]: value } : prize
    ));
  };

  const addPrize = () => {
    const newPrize: Prize = {
      id: Date.now().toString(),
      position: prizes.length + 1,
      title: `Position ${prizes.length + 1}`,
      amount: 0,
      currency: 'IDR',
    };
    setPrizes(prev => [...prev, newPrize]);
  };

  const removePrize = (id: string) => {
    setPrizes(prev => prev.filter(prize => prize.id !== id));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !formData) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const contactInfo: ContactInfo = {
        name: formData.contactName.trim(),
        phone: formData.contactPhone.trim(),
        email: formData.contactEmail.trim().toLowerCase(),
        website: formData.contactWebsite.trim() || undefined,
      };

      const updatedCompetition = {
        ...competition!,
        name: formData.name.trim(),
        description: formData.description.trim(),
        date: formData.date,
        endDate: formData.endDate || undefined,
        location: formData.location.trim(),
        venue: formData.venue.trim(),
        organizer: formData.organizer.trim(),
        category: formData.category,
        maxParticipants: formData.maxParticipants,
        registrationDeadline: formData.registrationDeadline,
        entryFee: formData.entryFee,
        currency: formData.currency,
        maxRounds: formData.maxRounds,
        rules: formData.rules.trim(),
        prizes: prizes.filter(prize => prize.amount > 0),
        contactInfo,
        updatedAt: new Date(),
      };

      // In real app, this would be an API call
      console.log('Updated competition:', updatedCompetition);

      toast({
        title: "Success!",
        description: `Competition "${formData.name}" has been updated successfully`,
        variant: "default",
      });

      // Navigate back to competition details
      navigate(`/competitions/${id}`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update competition. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/competitions/${id}`);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" disabled>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="h-8 w-64 bg-muted animate-pulse rounded" />
            <div className="h-4 w-48 bg-muted animate-pulse rounded mt-2" />
          </div>
        </div>
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <div className="h-64 bg-muted animate-pulse rounded-lg" />
            <div className="h-48 bg-muted animate-pulse rounded-lg" />
          </div>
          <div className="space-y-6">
            <div className="h-32 bg-muted animate-pulse rounded-lg" />
            <div className="h-48 bg-muted animate-pulse rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  if (!competition || !formData) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={() => navigate('/competitions')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Competition Not Found</h1>
            <p className="text-muted-foreground">
              The competition you're trying to edit doesn't exist.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Competition</h1>
          <p className="text-muted-foreground">
            Update competition details and settings
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Update the basic details of the competition
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Competition Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter competition name"
                    className={errors.name ? 'border-destructive' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter competition description"
                    rows={3}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="organizer">Organizer *</Label>
                    <Input
                      id="organizer"
                      value={formData.organizer}
                      onChange={(e) => handleInputChange('organizer', e.target.value)}
                      placeholder="Enter organizer name"
                      className={errors.organizer ? 'border-destructive' : ''}
                    />
                    {errors.organizer && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.organizer}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Regional">Regional</SelectItem>
                        <SelectItem value="National">National</SelectItem>
                        <SelectItem value="Open">Open</SelectItem>
                        <SelectItem value="Championship">Championship</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex gap-3 pt-6">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Updating Competition...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Competition
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          </form>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Current Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge variant="secondary" className="bg-green-500 text-white">
                  {competition.status}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Participants:</span>
                <span className="text-sm font-medium">
                  {competition.participants.length} / {formData.maxParticipants}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Current Round:</span>
                <span className="text-sm font-medium">
                  {competition.currentRound} / {formData.maxRounds}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Created:</span>
                <span className="text-sm font-medium">
                  {format(competition.createdAt, "PPP")}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Edit Guidelines */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Edit Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Important Notes</h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Changes to max rounds may affect ongoing scoring</li>
                  <li>• Reducing max participants may affect registrations</li>
                  <li>• Date changes should be communicated to participants</li>
                  <li>• Prize changes are effective immediately</li>
                </ul>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium">Restrictions</h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Cannot change competition ID</li>
                  <li>• Cannot modify participant list here</li>
                  <li>• Status changes require separate action</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
