# Contoh Pen<PERSON>an Aturan Validasi Scoring

## Contoh Skenario Scoring yang Valid

### Skenario 1: Perfect Score
```typescript
const perfectScoring: ScoringCriteria = {
  suaraDepan: 9,      // Maksimal
  suaraTengah: 9,     // Maksimal  
  suaraUjung: 9,      // Maksimal
  irama: 9,           // ✅ Valid: sama dengan max front sounds (9)
  mutuSuara: 9        // ✅ Valid: sama dengan max semua kriteria (9)
};
// Total: 45 (Perfect Performance)
```

### Skenario 2: Excellent Score
```typescript
const excellentScoring: ScoringCriteria = {
  suaraDepan: 9,      
  suaraTengah: 8.75,  
  suaraUjung: 8.5,    
  irama: 8.75,        // ✅ Valid: kurang dari max front sounds (9)
  mutuSuara: 8.75     // ✅ Valid: kurang dari max semua kriteria (9)
};
// Total: 43.75 (Perfect Performance)
```

### Skenario 3: Very Good Score
```typescript
const veryGoodScoring: ScoringCriteria = {
  suaraDepan: 8.75,   
  suaraTengah: 8.75,  
  suaraUjung: 8.5,    
  irama: 8.5,         // ✅ Valid: kurang dari max front sounds (8.75)
  mutuSuara: 8.5      // ✅ Valid: kurang dari max semua kriteria (8.75)
};
// Total: 43.25 (Very Good Performance)
```

## Contoh Skenario Scoring yang Tidak Valid

### Skenario 4: Irama Melebihi SEMUA Front Sounds
```typescript
const invalidIrama: ScoringCriteria = {
  suaraDepan: 8.5,
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 8.75,        // ❌ Invalid: melebihi SEMUA front sounds
  mutuSuara: 8
};
// Error: "Score Irama tidak boleh melebihi semua score Suara Depan, Suara Tengah, dan Suara Ujung. Setidaknya satu dari ketiga kriteria harus memiliki nilai >= score Irama"
```

### Skenario 5: Mutu Suara Melebihi SEMUA Kriteria Lain
```typescript
const invalidMutuSuara: ScoringCriteria = {
  suaraDepan: 8.5,
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 8,
  mutuSuara: 8.75     // ❌ Invalid: melebihi SEMUA kriteria lain
};
// Error: "Score Mutu Suara tidak boleh melebihi semua score Suara Depan, Suara Tengah, Suara Ujung, dan Irama. Setidaknya satu dari keempat kriteria harus memiliki nilai >= score Mutu Suara"
```

### Skenario 6: Kedua Aturan Dilanggar
```typescript
const invalidBoth: ScoringCriteria = {
  suaraDepan: 8.5,
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 8.75,        // ❌ Invalid: melebihi SEMUA front sounds
  mutuSuara: 9        // ❌ Invalid: melebihi SEMUA kriteria lain
};
// Errors:
// - "Score Irama tidak boleh melebihi semua score Suara Depan, Suara Tengah, dan Suara Ujung..."
// - "Score Mutu Suara tidak boleh melebihi semua score Suara Depan, Suara Tengah, Suara Ujung, dan Irama..."
```

## Contoh Skenario yang Menunjukkan Perbedaan Aturan

### Skenario 7: Irama Tinggi tapi Masih Valid (Aturan Baru)
```typescript
const validIramaHigh: ScoringCriteria = {
  suaraDepan: 9,      // Satu kriteria tinggi
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 8.75,        // ✅ Valid: karena suaraDepan (9) >= irama (8.75)
  mutuSuara: 8
};
// Valid karena setidaknya ada satu front sound (suaraDepan) yang >= irama
```

### Skenario 8: Mutu Suara Tinggi tapi Masih Valid (Aturan Baru)
```typescript
const validMutuSuaraHigh: ScoringCriteria = {
  suaraDepan: 8.5,
  suaraTengah: 8.5,
  suaraUjung: 8.5,
  irama: 9,           // Satu kriteria tinggi
  mutuSuara: 8.75     // ✅ Valid: karena irama (9) >= mutuSuara (8.75)
};
// Valid karena setidaknya ada satu kriteria (irama) yang >= mutu suara
```

## Contoh Edge Cases

### Edge Case 1: Irama Sama dengan Salah Satu Front Sound
```typescript
const edgeCase1: ScoringCriteria = {
  suaraDepan: 9,      // Tertinggi
  suaraTengah: 8.5,
  suaraUjung: 8.75,
  irama: 8.75,        // ✅ Valid: sama dengan suaraUjung, dan suaraDepan >= irama
  mutuSuara: 8.5
};
// Valid karena suaraDepan (9) >= irama (8.75)
```

### Edge Case 2: Mutu Suara Sama dengan Irama
```typescript
const edgeCase2: ScoringCriteria = {
  suaraDepan: 8.75,   
  suaraTengah: 8.5,   
  suaraUjung: 8.5,    
  irama: 8,           // Terendah
  mutuSuara: 8        // ✅ Valid: sama dengan irama
};
// Valid karena mutuSuara (8) <= max semua kriteria (8.75)
```

### Edge Case 3: Semua Nilai Minimum
```typescript
const minimumScoring: ScoringCriteria = {
  suaraDepan: 8.5,    // Minimum untuk front sounds
  suaraTengah: 8.5,   
  suaraUjung: 8.5,    
  irama: 8,           // ✅ Valid: kurang dari front sounds
  mutuSuara: 8        // ✅ Valid: sama dengan irama (terendah)
};
// Total: 41.5 (Average Performance)
```

## Implementasi dalam UI

### Real-time Validation
Saat user mengisi form scoring, validasi dilakukan secara real-time:

1. **Input Suara Depan**: User memilih 9
2. **Input Suara Tengah**: User memilih 8.75
3. **Input Suara Ujung**: User memilih 8.5
4. **Input Irama**: 
   - Jika user memilih 8.75 → ✅ Valid (kurang dari max 9)
   - Jika user memilih 9 → ✅ Valid (sama dengan max 9)
   - Jika user coba pilih nilai > 9 → Tidak tersedia di dropdown
5. **Input Mutu Suara**:
   - Jika user memilih 8.5 → ✅ Valid (kurang dari max 9)
   - Jika user memilih 9 → ✅ Valid (sama dengan max 9)

### Error Display
Jika terjadi error validasi:
```jsx
<div className="bg-destructive/10 border border-destructive/20 rounded-xl p-3 sm:p-4 space-y-2">
  <h4 className="font-bold text-destructive text-sm">Validation Errors:</h4>
  <div className="flex items-center gap-2 text-xs sm:text-sm text-destructive">
    <AlertCircle className="h-4 w-4 flex-shrink-0" />
    <span className="font-medium">Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung</span>
  </div>
</div>
```

### Submit Button State
```jsx
<Button
  onClick={handleSubmit}
  disabled={errors.length > 0 || total === 0}  // Disabled jika ada error
  className="w-full h-12 sm:h-14 text-sm sm:text-lg font-bold"
>
  Submit Score for Round {round}
</Button>
```

## Testing Validasi

Untuk menguji aturan validasi, jalankan:

```bash
npm test src/utils/__tests__/scoring.test.ts
```

Test cases mencakup:
- ✅ Validasi Rule 1 (Irama)
- ✅ Validasi Rule 2 (Mutu Suara)  
- ✅ Kombinasi kedua aturan
- ✅ Edge cases
- ✅ Utility functions

## Kesimpulan

Aturan validasi scoring memastikan:
1. **Konsistensi**: Irama tidak bisa lebih tinggi dari kualitas suara dasar
2. **Logika**: Mutu suara keseluruhan tidak bisa melebihi komponen pembentuknya
3. **User Experience**: Validasi real-time dengan feedback yang jelas
4. **Reliability**: Testing yang komprehensif untuk semua skenario
