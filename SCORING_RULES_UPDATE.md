# Update Aturan Validasi Scoring - PERKUTUT_KOMPETISI

## Perubahan Aturan Validasi

### <PERSON><PERSON><PERSON> (Salah)
**Rule 1**: Score Irama tidak boleh melebihi **maksimum** dari score suara depan, tengah, dan ujung
**Rule 2**: Score Mutu Suara tidak boleh melebihi **maksimum** dari semua kriteria lain

### Aturan Baru (Benar)
**Rule 1**: Score Irama tidak boleh melebihi **salah satu** dari score suara depan, tengah, dan ujung
**Rule 2**: Score Mutu Suara tidak boleh melebihi **salah satu** dari semua kriteria lain

## Perbedaan Implementasi

### Rule 1: Validasi Score Irama

**Implementasi Lama**:
```typescript
// SALAH: Menggunakan Math.max()
const maxFrontSounds = Math.max(criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung);
if (criteria.irama > maxFrontSounds) {
  // Error hanya jika irama > maksimum dari ketiga kriteria
}
```

**Implementasi Baru**:
```typescript
// BENAR: Menggunakan Array.some()
const frontSounds = [criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung];
const hasValidFrontSound = frontSounds.some(score => score >= criteria.irama);
if (!hasValidFrontSound) {
  // Error jika irama > SEMUA kriteria (tidak ada yang >= irama)
}
```

### Rule 2: Validasi Score Mutu Suara

**Implementasi Lama**:
```typescript
// SALAH: Menggunakan Math.max()
const maxOtherCriteria = Math.max(criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung, criteria.irama);
if (criteria.mutuSuara > maxOtherCriteria) {
  // Error hanya jika mutu suara > maksimum dari keempat kriteria
}
```

**Implementasi Baru**:
```typescript
// BENAR: Menggunakan Array.some()
const allOtherCriteria = [criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung, criteria.irama];
const hasValidOtherCriteria = allOtherCriteria.some(score => score >= criteria.mutuSuara);
if (!hasValidOtherCriteria) {
  // Error jika mutu suara > SEMUA kriteria (tidak ada yang >= mutu suara)
}
```

## Contoh Perbedaan Hasil

### Contoh 1: Score Irama
```typescript
const criteria = {
  suaraDepan: 9,      // Tinggi
  suaraTengah: 8.5,   // Rendah
  suaraUjung: 8.5,    // Rendah
  irama: 8.75,        // Di tengah
  mutuSuara: 8
};
```

**Aturan Lama**: ✅ Valid (karena 8.75 <= max(9, 8.5, 8.5) = 9)
**Aturan Baru**: ✅ Valid (karena suaraDepan (9) >= irama (8.75))
**Hasil**: Sama-sama valid

### Contoh 2: Score Irama (Kasus Berbeda)
```typescript
const criteria = {
  suaraDepan: 8.5,    
  suaraTengah: 8.5,   
  suaraUjung: 8.5,    
  irama: 8.75,        // Melebihi semua
  mutuSuara: 8
};
```

**Aturan Lama**: ❌ Invalid (karena 8.75 > max(8.5, 8.5, 8.5) = 8.5)
**Aturan Baru**: ❌ Invalid (karena tidak ada front sound yang >= 8.75)
**Hasil**: Sama-sama invalid

### Contoh 3: Score Mutu Suara (Kasus Penting)
```typescript
const criteria = {
  suaraDepan: 9,      // Tinggi
  suaraTengah: 8.5,   
  suaraUjung: 8.5,    
  irama: 8,           // Rendah
  mutuSuara: 8.75     // Di tengah
};
```

**Aturan Lama**: ✅ Valid (karena 8.75 <= max(9, 8.5, 8.5, 8) = 9)
**Aturan Baru**: ✅ Valid (karena suaraDepan (9) >= mutuSuara (8.75))
**Hasil**: Sama-sama valid

## Pesan Error yang Diperbarui

### Rule 1 - Pesan Error Baru:
```
"Score Irama tidak boleh melebihi semua score Suara Depan, Suara Tengah, dan Suara Ujung. Setidaknya satu dari ketiga kriteria harus memiliki nilai >= score Irama"
```

### Rule 2 - Pesan Error Baru:
```
"Score Mutu Suara tidak boleh melebihi semua score Suara Depan, Suara Tengah, Suara Ujung, dan Irama. Setidaknya satu dari keempat kriteria harus memiliki nilai >= score Mutu Suara"
```

## File yang Diperbarui

1. **`src/utils/scoring.ts`** - Logika validasi utama
2. **`src/components/scoring/ScoringForm.tsx`** - Validasi real-time di form
3. **`src/utils/__tests__/scoring.test.ts`** - Test cases yang diperbarui
4. **`SCORING_RULES.md`** - Dokumentasi aturan
5. **`SCORING_EXAMPLES.md`** - Contoh penggunaan

## Hasil Testing

✅ **15 test cases berhasil** dengan aturan baru:
- Rule 1: Score Irama Validation (4 tests)
- Rule 2: Score Mutu Suara Validation (4 tests)
- Combined Rules Validation (2 tests)
- Edge Cases (2 tests)
- Utility Functions (3 tests)

## Kesimpulan

Perubahan ini memastikan bahwa:

1. **Rule 1**: Score Irama hanya invalid jika melebihi **SEMUA** score suara (depan, tengah, ujung)
2. **Rule 2**: Score Mutu Suara hanya invalid jika melebihi **SEMUA** kriteria lain

Aturan ini lebih masuk akal karena:
- Memungkinkan fleksibilitas dalam penilaian
- Setidaknya ada satu aspek yang mendukung score irama/mutu suara
- Mencegah score yang benar-benar tidak realistis (melebihi semua kriteria)

**Status**: ✅ Implementasi selesai dan telah diuji
