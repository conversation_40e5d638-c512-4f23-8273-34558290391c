# PERKUTUT_KOMPETISI - Project Structure

## Overview
This is a React TypeScript application for managing bird competitions (Perkutut competitions), built with modern web technologies including Vite, shadcn/ui, and Tailwind CSS.

## Current Project Structure

```
PERKUTUT_KOMPETISI/
├── public/                     # Static assets
│   ├── favicon.ico
│   ├── placeholder.svg
│   └── robots.txt
├── src/                        # Source code
│   ├── components/             # React components
│   │   ├── competition/        # Competition-specific components
│   │   ├── scoring/           # Scoring system components
│   │   └── ui/                # shadcn/ui components
│   ├── hooks/                 # Custom React hooks
│   │   ├── use-mobile.tsx
│   │   ├── use-toast.ts
│   │   └── useScoring.ts
│   ├── lib/                   # Utility libraries
│   │   └── utils.ts
│   ├── pages/                 # Page components
│   │   ├── Index.tsx
│   │   ├── CompetitionManagement.tsx
│   │   └── NotFound.tsx
│   ├── types/                 # TypeScript type definitions
│   │   └── scoring.ts
│   ├── utils/                 # Utility functions
│   │   └── scoring.ts
│   ├── App.tsx               # Main app component
│   ├── main.tsx              # App entry point
│   ├── index.css             # Global styles
│   ├── App.css               # App-specific styles
│   └── vite-env.d.ts         # Vite type definitions
├── README.md                  # Project documentation
├── package.json              # Dependencies and scripts
├── package-lock.json         # Lock file
├── bun.lockb                 # Bun lock file
├── vite.config.ts            # Vite configuration
├── tsconfig.json             # TypeScript configuration
├── tsconfig.app.json         # App-specific TS config
├── tsconfig.node.json        # Node-specific TS config
├── tailwind.config.ts        # Tailwind CSS configuration
├── postcss.config.js         # PostCSS configuration
├── eslint.config.js          # ESLint configuration
├── components.json           # shadcn/ui configuration
└── index.html                # HTML template
```

## Technology Stack

### Core Technologies
- **React 18.3.1** - UI library
- **TypeScript 5.5.3** - Type safety
- **Vite 5.4.1** - Build tool and dev server
- **React Router DOM 6.26.2** - Client-side routing

### UI & Styling
- **shadcn/ui** - Component library based on Radix UI
- **Tailwind CSS 3.4.11** - Utility-first CSS framework
- **Lucide React** - Icon library
- **next-themes** - Theme management

### State Management & Data Fetching
- **TanStack React Query 5.56.2** - Server state management
- **React Hook Form 7.53.0** - Form handling
- **Zod 3.23.8** - Schema validation

### Development Tools
- **ESLint** - Code linting
- **TypeScript ESLint** - TypeScript-specific linting
- **Autoprefixer** - CSS vendor prefixes
- **Lovable Tagger** - Development tooling

## Recommended Project Structure Enhancements

### 1. Feature-Based Organization
Consider organizing by features for better scalability:

```
src/
├── features/
│   ├── competition/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── utils/
│   ├── scoring/
│   │   ├── components/
│   │   ├── hooks/
│   │   ├── services/
│   │   ├── types/
│   │   └── utils/
│   └── participants/
│       ├── components/
│       ├── hooks/
│       ├── services/
│       ├── types/
│       └── utils/
```

### 2. Additional Directories to Consider
```
src/
├── api/                       # API client and endpoints
├── constants/                 # Application constants
├── contexts/                  # React contexts
├── layouts/                   # Layout components
├── services/                  # Business logic services
└── store/                     # Global state management
```

### 3. Testing Structure
```
src/
├── __tests__/                 # Global tests
├── components/
│   └── __tests__/            # Component tests
└── utils/
    └── __tests__/            # Utility tests
```

## Development Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build in development mode
- `npm run lint` - Run ESLint
- `npm run preview` - Preview production build

## Getting Started

1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Open browser to `http://localhost:5173`

## Key Features (Based on Current Structure)

1. **Competition Management** - Manage bird competitions
2. **Scoring System** - Handle competition scoring
3. **Responsive Design** - Mobile-friendly interface
4. **Modern UI** - shadcn/ui components with Tailwind CSS
5. **Type Safety** - Full TypeScript support
6. **Form Handling** - React Hook Form with Zod validation

## Next Steps for Development

1. **Add Authentication** - User login/registration system
2. **Database Integration** - Connect to backend API
3. **Real-time Updates** - WebSocket for live scoring
4. **Export Features** - PDF/Excel export for results
5. **Multi-language Support** - i18n implementation
6. **Testing Suite** - Unit and integration tests
7. **PWA Features** - Offline support and installability

This structure provides a solid foundation for a scalable bird competition management system.
