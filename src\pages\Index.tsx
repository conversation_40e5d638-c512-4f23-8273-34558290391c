import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { ParticipantCard } from '@/components/scoring/ParticipantCard';
import { ScoringForm } from '@/components/scoring/ScoringForm';
import { ScoringGrid } from '@/components/scoring/ScoringGrid';
import { useScoring } from '@/hooks/useScoring';
import { Participant, ScoringCriteria } from '@/types/scoring';
import { Users, BarChart3, Trophy, FileText, Grid3X3, LayoutGrid } from 'lucide-react';

const Index = () => {
  const {
    competition,
    scores,
    addScore,
    getParticipantScores,
    getCurrentRoundScore
  } = useScoring();

  const [selectedParticipant, setSelectedParticipant] = useState<Participant | null>(null);
  const [scoringDialogOpen, setScoringDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('scoring');
  const [viewMode, setViewMode] = useState<'card' | 'grid'>('card');

  const handleScore = (participant: Participant) => {
    setSelectedParticipant(participant);
    setScoringDialogOpen(true);
  };

  const handleSubmitScore = (criteria: ScoringCriteria, total: number) => {
    if (selectedParticipant) {
      addScore(selectedParticipant.id, criteria);
      setScoringDialogOpen(false);
      setSelectedParticipant(null);
    }
  };

  const handleView = (participant: Participant) => {
    setSelectedParticipant(participant);
    // TODO: Implement view dialog
  };

  const currentRoundScore = selectedParticipant 
    ? getCurrentRoundScore(selectedParticipant.id)
    : undefined;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/30">
      <div className="container mx-auto px-4 py-6 lg:py-8 space-y-8">


        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full space-y-6">
          <div className="flex justify-center">
            <TabsList className="grid grid-cols-4 w-full max-w-2xl h-14 p-1 bg-muted/50 backdrop-blur-sm">
              <TabsTrigger value="scoring" className="flex items-center gap-2 text-sm font-medium py-3">
                <Users className="h-4 w-4" />
                <span className="hidden sm:inline">Scoring</span>
              </TabsTrigger>
              <TabsTrigger value="results" className="flex items-center gap-2 text-sm font-medium py-3">
                <BarChart3 className="h-4 w-4" />
                <span className="hidden sm:inline">Results</span>
              </TabsTrigger>
              <TabsTrigger value="recap" className="flex items-center gap-2 text-sm font-medium py-3">
                <Trophy className="h-4 w-4" />
                <span className="hidden sm:inline">Perumusan</span>
              </TabsTrigger>
              <TabsTrigger value="reports" className="flex items-center gap-2 text-sm font-medium py-3">
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Reports</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="scoring" className="space-y-6">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-6">
              <div className="text-center lg:text-left">
                <h2 className="text-2xl lg:text-3xl font-bold text-foreground mb-2">Round {competition.currentRound} Scoring</h2>
                <p className="text-muted-foreground text-lg">Score each participant according to P3SI standards</p>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center gap-2 bg-muted/30 rounded-lg p-1">
                <Button
                  variant={viewMode === 'card' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('card')}
                  className="flex items-center gap-2"
                >
                  <LayoutGrid className="h-4 w-4" />
                  Card Mode
                </Button>
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="flex items-center gap-2"
                >
                  <Grid3X3 className="h-4 w-4" />
                  Grid Mode
                </Button>
              </div>
            </div>

            {/* Card Mode */}
            {viewMode === 'card' && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 lg:gap-6">
                {competition.participants.map(participant => (
                  <ParticipantCard
                    key={participant.id}
                    participant={participant}
                    scores={getParticipantScores(participant.id)}
                    currentRound={competition.currentRound}
                    onScore={() => handleScore(participant)}
                    onView={() => handleView(participant)}
                  />
                ))}
              </div>
            )}

            {/* Grid Mode */}
            {viewMode === 'grid' && (
              <ScoringGrid
                participants={competition.participants}
                scores={scores}
                currentRound={competition.currentRound}
                onScore={handleScore}
                getParticipantScores={getParticipantScores}
              />
            )}
          </TabsContent>

          <TabsContent value="results" className="space-y-6">
            <div className="bg-card rounded-xl border shadow-sm p-8 lg:p-12 text-center">
              <div className="mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mb-6">
                <BarChart3 className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-2xl font-bold mb-3">Competition Results</h3>
              <p className="text-muted-foreground text-lg max-w-md mx-auto">
                Results and rankings will be displayed here after scoring is complete for all rounds.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="recap" className="space-y-6">
            <div className="bg-card rounded-xl border shadow-sm p-8 lg:p-12 text-center">
              <div className="mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mb-6">
                <Trophy className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-2xl font-bold mb-3">Score Recap (Perumusan)</h3>
              <p className="text-muted-foreground text-lg max-w-md mx-auto">
                Comprehensive scoring recap across all rounds with detailed participant analytics and final rankings.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            <div className="bg-card rounded-xl border shadow-sm p-8 lg:p-12 text-center">
              <div className="mx-auto w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mb-6">
                <FileText className="h-10 w-10 text-primary" />
              </div>
              <h3 className="text-2xl font-bold mb-3">Competition Reports</h3>
              <p className="text-muted-foreground text-lg max-w-md mx-auto">
                Generate and export detailed competition reports, statistics, and official P3SI documentation.
              </p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Scoring Dialog */}
        <Dialog open={scoringDialogOpen} onOpenChange={setScoringDialogOpen}>
          <DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] p-0 border-0">
            <div className="bg-gradient-to-br from-background to-muted/30 rounded-lg h-full flex flex-col">
              <DialogHeader className="p-4 sm:p-6 pb-4 border-b border-border/30 flex-shrink-0">
                <DialogTitle className="text-xl sm:text-2xl font-bold text-foreground">Score Participant</DialogTitle>
              </DialogHeader>
              <div className="flex-1 overflow-y-auto p-4 sm:p-6">
                {selectedParticipant && (
                  <ScoringForm
                    participant={selectedParticipant}
                    round={competition.currentRound}
                    onSubmit={handleSubmitScore}
                    initialScoring={currentRoundScore?.criteria}
                  />
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default Index;
