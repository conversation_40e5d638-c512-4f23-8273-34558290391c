import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Users, Trash2, AlertCircle } from 'lucide-react';
import { Participant } from '@/types/scoring';

interface BlockCardProps {
  blockNumber: number;
  participants: Participant[];
  maxParticipants: number;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
}

export function BlockCard({ 
  blockNumber, 
  participants, 
  maxParticipants, 
  isSelected, 
  onSelect, 
  onDelete 
}: BlockCardProps) {
  const participantCount = participants.length;
  const progressPercentage = (participantCount / maxParticipants) * 100;
  const isFull = participantCount >= maxParticipants;
  const isEmpty = participantCount === 0;

  return (
    <Card 
      className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
        isSelected ? 'ring-2 ring-primary border-primary' : ''
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            <span className="font-semibold">Block {blockNumber}</span>
          </div>
          <div className="flex items-center gap-2">
            {isFull && (
              <AlertCircle className="h-4 w-4 text-yellow-600" />
            )}
            <Badge 
              variant={isFull ? "secondary" : isEmpty ? "outline" : "default"}
              className={
                isFull ? "bg-yellow-100 text-yellow-800 border-yellow-300" : ""
              }
            >
              {participantCount}/{maxParticipants}
            </Badge>
            <Button
              size="sm"
              variant="ghost"
              onClick={(e) => {
                e.stopPropagation();
                onDelete();
              }}
              className="text-destructive hover:text-destructive h-8 w-8 p-0"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Participants</span>
            <span className={`font-medium ${isFull ? 'text-yellow-700' : ''}`}>
              {participantCount} of {maxParticipants}
            </span>
          </div>
          <Progress 
            value={progressPercentage} 
            className="h-2"
          />
          {isFull && (
            <p className="text-xs text-yellow-700 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              Block is full
            </p>
          )}
        </div>

        {participantCount > 0 && (
          <div className="mt-3 pt-3 border-t border-border/30">
            <p className="text-xs text-muted-foreground mb-2">Recent participants:</p>
            <div className="text-xs space-y-1">
              {participants.slice(-2).map((participant, index) => (
                <div key={participant.id} className="flex justify-between">
                  <span className="truncate mr-2">{participant.name}</span>
                  <span className="text-muted-foreground">Ring {participant.ringNumber}</span>
                </div>
              ))}
              {participantCount > 2 && (
                <p className="text-muted-foreground italic">
                  +{participantCount - 2} more...
                </p>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}