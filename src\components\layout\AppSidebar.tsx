import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Trophy,
  Users,
  UserCheck,
  BarChart3,
  FileText,
  Settings,
  Calendar,
  Award,
  Target,
  Database,
  ChevronRight,
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

const menuItems = [
  {
    title: 'Dashboard',
    url: '/dashboard',
    icon: Home,
  },
  {
    title: 'Competitions',
    icon: Trophy,
    items: [
      {
        title: 'All Competitions',
        url: '/competitions',
        icon: Trophy,
      },
      {
        title: 'Create Competition',
        url: '/competitions/create',
        icon: Calendar,
      },
      {
        title: 'Competition Settings',
        url: '/competitions/settings',
        icon: Settings,
      },
    ],
  },
  {
    title: 'Participants',
    icon: Users,
    items: [
      {
        title: 'All Participants',
        url: '/participants',
        icon: Users,
      },
      {
        title: 'Add Participant',
        url: '/participants/add',
        icon: UserCheck,
      },
      {
        title: 'Participant Categories',
        url: '/participants/categories',
        icon: Target,
      },
    ],
  },
  {
    title: 'Scoring',
    icon: BarChart3,
    items: [
      {
        title: 'Live Scoring',
        url: '/scoring',
        icon: BarChart3,
      },
      {
        title: 'Score Review',
        url: '/scoring/review',
        icon: FileText,
      },
      {
        title: 'Score History',
        url: '/scoring/history',
        icon: Database,
      },
    ],
  },
  {
    title: 'Results',
    icon: Award,
    items: [
      {
        title: 'Competition Results',
        url: '/results',
        icon: Award,
      },
      {
        title: 'Rankings',
        url: '/results/rankings',
        icon: Trophy,
      },
      {
        title: 'Statistics',
        url: '/results/statistics',
        icon: BarChart3,
      },
    ],
  },
  {
    title: 'Reports',
    url: '/reports',
    icon: FileText,
  },
  {
    title: 'Settings',
    url: '/settings',
    icon: Settings,
  },
];

export function AppSidebar() {
  const location = useLocation();

  return (
    <Sidebar variant="inset" className="border-r">
      <SidebarHeader className="border-b px-6 py-4">
        <div className="flex items-center gap-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <Trophy className="h-6 w-6" />
          </div>
          <div className="flex flex-col">
            <span className="text-lg font-semibold">Perkutut</span>
            <span className="text-xs text-muted-foreground">Competition System</span>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Navigation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  {item.items ? (
                    <Collapsible className="group/collapsible">
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton className="w-full justify-between">
                          <div className="flex items-center gap-2">
                            <item.icon className="h-4 w-4" />
                            <span>{item.title}</span>
                          </div>
                          <ChevronRight className="h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-90" />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {item.items.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton
                                asChild
                                isActive={location.pathname === subItem.url}
                              >
                                <Link to={subItem.url}>
                                  <subItem.icon className="h-4 w-4" />
                                  <span>{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </Collapsible>
                  ) : (
                    <SidebarMenuButton
                      asChild
                      isActive={location.pathname === item.url}
                    >
                      <Link to={item.url!}>
                        <item.icon className="h-4 w-4" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t p-4">
        <div className="text-xs text-muted-foreground text-center">
          <p>P3SI Competition System</p>
          <p>Version 1.0.0</p>
        </div>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
}
