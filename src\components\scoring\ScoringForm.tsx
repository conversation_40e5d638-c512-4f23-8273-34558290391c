import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertCircle } from 'lucide-react';
import { ScoringCriteria, Participant, ALLOWED_VALUES, CRITERIA_LABELS } from '@/types/scoring';
import { calculateTotal, formatScore, getScoreCategory } from '@/utils/scoring';

interface ScoringFormProps {
  participant: Participant;
  round: number;
  onSubmit: (criteria: ScoringCriteria, total: number) => void;
  initialScoring?: ScoringCriteria;
}

// Custom validation function that handles empty values
function validateScoringForm(criteria: ScoringCriteria): string[] {
  const errors: string[] = [];
  
  // Only validate if values are not 0 (empty state)
  if (criteria.suaraDepan !== 0 && !(ALLOWED_VALUES.suaraDepan as readonly number[]).includes(criteria.suaraDepan)) {
    errors.push('Nilai Suara Depan harus 9, 8.75, atau 8.5');
  }
  
  if (criteria.suaraTengah !== 0 && !(ALLOWED_VALUES.suaraTengah as readonly number[]).includes(criteria.suaraTengah)) {
    errors.push('Nilai Suara Tengah harus 9, 8.75, atau 8.5');
  }
  
  if (criteria.suaraUjung !== 0 && !(ALLOWED_VALUES.suaraUjung as readonly number[]).includes(criteria.suaraUjung)) {
    errors.push('Nilai Suara Ujung harus 8.75 atau 8.5');
  }
  
  if (criteria.irama !== 0 && !(ALLOWED_VALUES.irama as readonly number[]).includes(criteria.irama)) {
    errors.push('Nilai Irama harus 8.75, 8.5, atau 8');
  }
  
  if (criteria.mutuSuara !== 0 && !(ALLOWED_VALUES.mutuSuara as readonly number[]).includes(criteria.mutuSuara)) {
    errors.push('Nilai Mutu Suara harus 8.75, 8.5, atau 8');
  }
  
  // Only check constraints if any values are set to provide real-time feedback
  if (criteria.irama !== 0 && (criteria.suaraDepan !== 0 || criteria.suaraTengah !== 0 || criteria.suaraUjung !== 0)) {
    const frontSounds = [criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung].filter(val => val !== 0);
    if (frontSounds.length > 0) {
      const maxFrontSounds = Math.max(...frontSounds);
      if (criteria.irama > maxFrontSounds) {
        errors.push('Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung');
      }
    }
  }
  
  if (criteria.mutuSuara !== 0) {
    const otherCriteria = [criteria.suaraDepan, criteria.suaraTengah, criteria.suaraUjung, criteria.irama].filter(val => val !== 0);
    if (otherCriteria.length > 0) {
      const maxOtherCriteria = Math.max(...otherCriteria);
      if (criteria.mutuSuara > maxOtherCriteria) {
        errors.push('Score Mutu Suara tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, Suara Ujung, dan Irama');
      }
    }
  }
  
  return errors;
}

export function ScoringForm({ participant, round, onSubmit, initialScoring }: ScoringFormProps) {
  const [criteria, setCriteria] = useState<ScoringCriteria>(
    initialScoring || {
      suaraDepan: 0,
      suaraTengah: 0,
      suaraUjung: 0,
      irama: 0,
      mutuSuara: 0
    }
  );

  const [errors, setErrors] = useState<string[]>([]);
  const [total, setTotal] = useState(0);

  useEffect(() => {
    const validationErrors = validateScoringForm(criteria);
    setErrors(validationErrors);
    
    // Only calculate total if no validation errors and all values are set
    const allValuesSet = criteria.suaraDepan !== 0 && criteria.suaraTengah !== 0 && 
                         criteria.suaraUjung !== 0 && criteria.irama !== 0 && criteria.mutuSuara !== 0;
    
    if (validationErrors.length === 0 && allValuesSet) {
      setTotal(calculateTotal(criteria));
    } else {
      setTotal(0);
    }
  }, [criteria]);

  const updateCriteria = (key: keyof ScoringCriteria, value: number) => {
    setCriteria(prev => ({ ...prev, [key]: value }));
  };

  const handleSubmit = () => {
    if (errors.length === 0 && total > 0) {
      onSubmit(criteria, total);
    }
  };

  const scoreCategory = getScoreCategory(total);

  return (
    <div className="w-full space-y-4 sm:space-y-6">
      <div className="bg-card rounded-xl border shadow-sm p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4 sm:mb-6">
          <div className="min-w-0 flex-1">
            <div className="text-xl sm:text-2xl font-bold text-foreground mb-2 truncate">{participant.name}</div>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-xs sm:text-sm text-muted-foreground">
              <div>Ring: <span className="font-medium text-foreground">{participant.ringNumber}</span></div>
              <div className="truncate">Owner: <span className="font-medium text-foreground">{participant.owner}</span></div>
              <div>Round: <span className="font-medium text-foreground">{round}</span></div>
            </div>
          </div>
          {total > 0 && (
            <Badge 
              variant={scoreCategory === 'perfect' ? 'perfect' : 
                      scoreCategory === 'excellent' ? 'excellent' :
                      scoreCategory === 'very-good' ? 'very-good' :
                      scoreCategory === 'good' ? 'good' : 'secondary'}
              className="text-sm sm:text-lg px-4 sm:px-6 py-2 sm:py-3 font-bold shadow-lg flex-shrink-0"
            >
              {formatScore(total)}
            </Badge>
          )}
        </div>
      
        {/* Scoring Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          {(Object.keys(CRITERIA_LABELS) as Array<keyof ScoringCriteria>).map((key) => (
            <div key={key} className="space-y-2">
              <Label className="text-xs sm:text-sm font-bold text-foreground uppercase tracking-wide block">
                {CRITERIA_LABELS[key]}
              </Label>
              <Select
                value={criteria[key] === 0 ? "" : criteria[key].toString()}
                onValueChange={(value) => updateCriteria(key, parseFloat(value))}
              >
                <SelectTrigger className="w-full h-10 sm:h-12 text-sm sm:text-base font-semibold bg-background/50 border-2 hover:bg-background/80 transition-all duration-200">
                  <SelectValue placeholder="Select score" />
                </SelectTrigger>
                <SelectContent className="bg-background/95 backdrop-blur-sm border-2">
                  {ALLOWED_VALUES[key].map((value) => (
                    <SelectItem key={value} value={value.toString()} className="text-sm sm:text-base font-medium py-2 sm:py-3">
                      {value}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          ))}
        </div>
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-3 sm:p-4 space-y-2">
          <h4 className="font-bold text-destructive text-sm">Validation Errors:</h4>
          {errors.map((error, index) => (
            <div key={index} className="flex items-center gap-2 text-xs sm:text-sm text-destructive">
              <AlertCircle className="h-4 w-4 flex-shrink-0" />
              <span className="font-medium">{error}</span>
            </div>
          ))}
        </div>
      )}

      {/* Total Score Display */}
      {total > 0 && (
        <div className="bg-gradient-to-br from-muted/30 to-muted/50 border rounded-xl p-4 sm:p-6 text-center">
          <div className="space-y-2 sm:space-y-3">
            <div className="text-xs sm:text-sm text-muted-foreground uppercase tracking-wide font-bold">Total Score</div>
            <div className={`text-3xl sm:text-4xl lg:text-5xl font-bold ${
              scoreCategory === 'perfect' ? 'text-score-perfect' :
              scoreCategory === 'excellent' ? 'text-score-excellent' :
              scoreCategory === 'very-good' ? 'text-score-very-good' :
              scoreCategory === 'good' ? 'text-score-good' : 'text-score-average'
            }`}>
              {formatScore(total)}
            </div>
            <Badge 
              variant={scoreCategory === 'perfect' ? 'perfect' : 
                      scoreCategory === 'excellent' ? 'excellent' :
                      scoreCategory === 'very-good' ? 'very-good' :
                      scoreCategory === 'good' ? 'good' : 'secondary'}
              className="text-xs sm:text-sm font-bold px-3 sm:px-4 py-1 sm:py-2 uppercase tracking-wide"
            >
              {scoreCategory.replace('-', ' ')} Performance
            </Badge>
          </div>
        </div>
      )}

      {/* Submit Button */}
      <Button
        onClick={handleSubmit}
        disabled={errors.length > 0 || total === 0}
        className="w-full h-12 sm:h-14 text-sm sm:text-lg font-bold transition-all duration-200 hover:scale-105 shadow-lg"
        variant="scoring"
        size="lg"
      >
        Submit Score for Round {round}
      </Button>
    </div>
  );
}