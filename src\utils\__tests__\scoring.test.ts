import { validateScoring, calculateTotal, formatScore, getScoreCategory } from '../scoring';
import { ScoringCriteria } from '@/types/scoring';

describe('Scoring Validation Rules', () => {
  describe('Rule 1: Score Irama Validation', () => {
    it('should pass when irama is equal to one of the front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 9, // Equal to suaraDepan (valid)
        mutuSuara: 8.5
      };

      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });

    it('should pass when irama is less than at least one front sound', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.75, // Less than suaraDepan, equal to suara<PERSON><PERSON><PERSON> (valid)
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });

    it('should fail when irama exceeds ALL front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75, // Exceeds ALL front sounds (invalid)
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi semua score Suara Depan, Suara Tengah, dan Suara Ujung. Setidaknya satu dari ketiga kriteria harus memiliki nilai >= score Irama');
    });

    it('should pass when irama equals the lowest front sound but others are higher', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,   // Lowest
        suaraTengah: 9,    // Highest
        suaraUjung: 8.75,  // Middle
        irama: 8.5,        // Equal to lowest (valid because suaraTengah >= irama)
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });
  });

  describe('Rule 2: Score Mutu Suara Validation', () => {
    it('should pass when mutu suara is equal to one of the other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,
        mutuSuara: 9 // Equal to suaraDepan (valid)
      };

      const errors = validateScoring(criteria);
      const mutuSuaraErrors = errors.filter(error => error.includes('Mutu Suara'));
      expect(mutuSuaraErrors).toHaveLength(0);
    });

    it('should pass when mutu suara is less than at least one other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.75,
        mutuSuara: 8.75 // Equal to suaraTengah and irama, less than suaraDepan (valid)
      };

      const errors = validateScoring(criteria);
      const mutuSuaraErrors = errors.filter(error => error.includes('Mutu Suara'));
      expect(mutuSuaraErrors).toHaveLength(0);
    });

    it('should fail when mutu suara exceeds ALL other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8,
        mutuSuara: 8.75 // Exceeds ALL other criteria (invalid)
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi semua score Suara Depan, Suara Tengah, Suara Ujung, dan Irama. Setidaknya satu dari keempat kriteria harus memiliki nilai >= score Mutu Suara');
    });

    it('should pass when mutu suara equals the lowest criteria but others are higher', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75,    // This will fail Rule 1, but for Rule 2 testing
        mutuSuara: 8.5  // Equal to front sounds, less than irama (valid for Rule 2)
      };

      const errors = validateScoring(criteria);
      // Should have error for irama rule, but mutu suara rule should be valid
      const mutuSuaraErrors = errors.filter(error => error.includes('Mutu Suara'));
      expect(mutuSuaraErrors).toHaveLength(0);
    });
  });

  describe('Combined Rules Validation', () => {
    it('should pass with valid scoring across all criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,     // Valid: suaraDepan >= irama
        mutuSuara: 8.5  // Valid: suaraDepan >= mutuSuara
      };

      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error =>
        error.includes('Irama tidak boleh melebihi') ||
        error.includes('Mutu Suara tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });

    it('should fail both rules when both are violated', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75,    // Invalid: exceeds ALL front sounds
        mutuSuara: 9    // Invalid: exceeds ALL other criteria
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi semua score Suara Depan, Suara Tengah, dan Suara Ujung. Setidaknya satu dari ketiga kriteria harus memiliki nilai >= score Irama');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi semua score Suara Depan, Suara Tengah, Suara Ujung, dan Irama. Setidaknya satu dari keempat kriteria harus memiliki nilai >= score Mutu Suara');
    });
  });

  describe('Edge Cases', () => {
    it('should handle minimum valid scores', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8,       // Valid: less than front sounds
        mutuSuara: 8    // Valid: equal to irama (lowest)
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });

    it('should handle maximum valid scores', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 9,
        suaraUjung: 9,
        irama: 9,       // Valid: equal to front sounds
        mutuSuara: 9    // Valid: equal to all criteria
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });
  });

  describe('Utility Functions', () => {
    it('should calculate total correctly', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,
        mutuSuara: 8.5
      };
      
      const total = calculateTotal(criteria);
      expect(total).toBe(43.25);
    });

    it('should format scores correctly', () => {
      expect(formatScore(43)).toBe('43');
      expect(formatScore(43.25)).toBe('43 1/4');
      expect(formatScore(43.5)).toBe('43 1/2');
      expect(formatScore(43.75)).toBe('43 3/4');
    });

    it('should categorize scores correctly', () => {
      expect(getScoreCategory(43.75)).toBe('perfect');
      expect(getScoreCategory(43.5)).toBe('excellent');
      expect(getScoreCategory(43.25)).toBe('very-good');
      expect(getScoreCategory(43)).toBe('good');
      expect(getScoreCategory(42.5)).toBe('average');
    });
  });
});
