import { validateScoring, calculateTotal, formatScore, getScoreCategory } from '../scoring';
import { ScoringCriteria } from '@/types/scoring';

describe('Scoring Validation Rules', () => {
  describe('Rule 1: Score Irama Validation', () => {
    it('should pass when irama is less than or equal to all front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5, // Less than or equal to all front sounds
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });

    it('should fail when irama exceeds suara depan', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 9,
        suaraUjung: 9,
        irama: 8.75, // Exceeds suaraDepan (invalid)
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Depan');
    });

    it('should fail when irama exceeds suara tengah', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.5,
        suaraUjung: 9,
        irama: 8.75, // Exceeds suaraTengah (invalid)
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Tengah');
    });

    it('should fail when irama exceeds suara ujung', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 9,
        suaraUjung: 8.5,
        irama: 8.75, // Exceeds suaraUjung (invalid)
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Ujung');
    });

    it('should fail with multiple errors when irama exceeds multiple front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75, // Exceeds all front sounds
        mutuSuara: 8
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Depan');
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Tengah');
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Ujung');
    });
  });

  describe('Rule 2: Score Mutu Suara Validation', () => {
    it('should pass when mutu suara is less than or equal to all other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,
        mutuSuara: 8.5 // Less than or equal to all other criteria
      };

      const errors = validateScoring(criteria);
      const mutuSuaraErrors = errors.filter(error => error.includes('Mutu Suara'));
      expect(mutuSuaraErrors).toHaveLength(0);
    });

    it('should fail when mutu suara exceeds suara depan', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 9,
        suaraUjung: 9,
        irama: 9,
        mutuSuara: 8.75 // Exceeds suaraDepan (invalid)
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Depan');
    });

    it('should fail when mutu suara exceeds suara tengah', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.5,
        suaraUjung: 9,
        irama: 9,
        mutuSuara: 8.75 // Exceeds suaraTengah (invalid)
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Tengah');
    });

    it('should fail when mutu suara exceeds suara ujung', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 9,
        suaraUjung: 8.5,
        irama: 9,
        mutuSuara: 8.75 // Exceeds suaraUjung (invalid)
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Ujung');
    });

    it('should fail when mutu suara exceeds irama', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 9,
        suaraUjung: 9,
        irama: 8.5,
        mutuSuara: 8.75 // Exceeds irama (invalid)
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Irama');
    });

    it('should fail with multiple errors when mutu suara exceeds multiple criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8,
        mutuSuara: 8.75 // Exceeds all other criteria
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Depan');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Tengah');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Ujung');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Irama');
    });
  });

  describe('Combined Rules Validation', () => {
    it('should pass with valid scoring across all criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,     // Valid: <= all front sounds
        mutuSuara: 8.5  // Valid: <= all other criteria
      };

      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error =>
        error.includes('Irama tidak boleh melebihi') ||
        error.includes('Mutu Suara tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });

    it('should fail the exact case from the user screenshot', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.75,   // OK
        suaraTengah: 8.5,   // Problem: < irama
        suaraUjung: 8.75,   // OK
        irama: 8.75,        // Invalid: exceeds suaraTengah
        mutuSuara: 8.75     // Invalid: exceeds suaraTengah
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Tengah');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Tengah');
    });

    it('should fail when multiple criteria are violated', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75,    // Invalid: exceeds all front sounds
        mutuSuara: 9    // Invalid: exceeds all other criteria
      };

      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Depan');
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Tengah');
      expect(errors).toContain('Score Irama tidak boleh melebihi score Suara Ujung');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Depan');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Tengah');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Suara Ujung');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi score Irama');
    });
  });

  describe('Edge Cases', () => {
    it('should handle minimum valid scores', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8,       // Valid: less than front sounds
        mutuSuara: 8    // Valid: equal to irama (lowest)
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });

    it('should handle maximum valid scores', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 9,
        suaraUjung: 9,
        irama: 9,       // Valid: equal to front sounds
        mutuSuara: 9    // Valid: equal to all criteria
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });
  });

  describe('Utility Functions', () => {
    it('should calculate total correctly', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,
        mutuSuara: 8.5
      };
      
      const total = calculateTotal(criteria);
      expect(total).toBe(43.25);
    });

    it('should format scores correctly', () => {
      expect(formatScore(43)).toBe('43');
      expect(formatScore(43.25)).toBe('43 1/4');
      expect(formatScore(43.5)).toBe('43 1/2');
      expect(formatScore(43.75)).toBe('43 3/4');
    });

    it('should categorize scores correctly', () => {
      expect(getScoreCategory(43.75)).toBe('perfect');
      expect(getScoreCategory(43.5)).toBe('excellent');
      expect(getScoreCategory(43.25)).toBe('very-good');
      expect(getScoreCategory(43)).toBe('good');
      expect(getScoreCategory(42.5)).toBe('average');
    });
  });
});
