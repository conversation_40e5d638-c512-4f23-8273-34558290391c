import { validateScoring, calculateTotal, formatScore, getScoreCategory } from '../scoring';
import { ScoringCriteria } from '@/types/scoring';

describe('Scoring Validation Rules', () => {
  describe('Rule 1: Score Irama Validation', () => {
    it('should pass when irama is equal to max front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 9, // Equal to max (suaraDepan)
        mutuSuara: 8.5
      };
      
      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });

    it('should pass when irama is less than max front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5, // Less than max (suaraDepan = 9)
        mutuSuara: 8
      };
      
      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });

    it('should fail when irama exceeds max front sounds', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75, // Exceeds max front sounds (8.5)
        mutuSuara: 8
      };
      
      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung');
    });

    it('should handle mixed front sound values correctly', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 9,    // Highest
        suaraUjung: 8.75,
        irama: 8.75,       // Equal to second highest, but less than max
        mutuSuara: 8
      };
      
      const errors = validateScoring(criteria);
      const iramaErrors = errors.filter(error => error.includes('Irama'));
      expect(iramaErrors).toHaveLength(0);
    });
  });

  describe('Rule 2: Score Mutu Suara Validation', () => {
    it('should pass when mutu suara is equal to max of all other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,
        mutuSuara: 9 // Equal to max (suaraDepan)
      };
      
      const errors = validateScoring(criteria);
      const mutuSuaraErrors = errors.filter(error => error.includes('Mutu Suara'));
      expect(mutuSuaraErrors).toHaveLength(0);
    });

    it('should pass when mutu suara is less than max of all other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.75,
        mutuSuara: 8.5 // Less than max (suaraDepan = 9)
      };
      
      const errors = validateScoring(criteria);
      const mutuSuaraErrors = errors.filter(error => error.includes('Mutu Suara'));
      expect(mutuSuaraErrors).toHaveLength(0);
    });

    it('should fail when mutu suara exceeds max of all other criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8,
        mutuSuara: 8.75 // Exceeds max of all other criteria (8.5)
      };
      
      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, Suara Ujung, dan Irama');
    });

    it('should consider irama in validation when irama is the highest', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75,    // Highest value (but this should fail Rule 1)
        mutuSuara: 8.5  // Less than irama, but irama itself is invalid
      };
      
      const errors = validateScoring(criteria);
      // Should have error for irama rule, but mutu suara should be valid relative to front sounds
      expect(errors).toContain('Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung');
    });
  });

  describe('Combined Rules Validation', () => {
    it('should pass with valid scoring across all criteria', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,     // Valid: <= max front sounds (9)
        mutuSuara: 8.5  // Valid: <= max all criteria (9)
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('Irama tidak boleh melebihi') || 
        error.includes('Mutu Suara tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });

    it('should fail both rules when both are violated', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8.75,    // Invalid: exceeds front sounds
        mutuSuara: 9    // Invalid: exceeds all other criteria
      };
      
      const errors = validateScoring(criteria);
      expect(errors).toContain('Score Irama tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, dan Suara Ujung');
      expect(errors).toContain('Score Mutu Suara tidak boleh melebihi nilai masing-masing score Suara Depan, Suara Tengah, Suara Ujung, dan Irama');
    });
  });

  describe('Edge Cases', () => {
    it('should handle minimum valid scores', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 8.5,
        suaraTengah: 8.5,
        suaraUjung: 8.5,
        irama: 8,       // Valid: less than front sounds
        mutuSuara: 8    // Valid: equal to irama (lowest)
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });

    it('should handle maximum valid scores', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 9,
        suaraUjung: 9,
        irama: 9,       // Valid: equal to front sounds
        mutuSuara: 9    // Valid: equal to all criteria
      };
      
      const errors = validateScoring(criteria);
      const ruleErrors = errors.filter(error => 
        error.includes('tidak boleh melebihi')
      );
      expect(ruleErrors).toHaveLength(0);
    });
  });

  describe('Utility Functions', () => {
    it('should calculate total correctly', () => {
      const criteria: ScoringCriteria = {
        suaraDepan: 9,
        suaraTengah: 8.75,
        suaraUjung: 8.5,
        irama: 8.5,
        mutuSuara: 8.5
      };
      
      const total = calculateTotal(criteria);
      expect(total).toBe(43.25);
    });

    it('should format scores correctly', () => {
      expect(formatScore(43)).toBe('43');
      expect(formatScore(43.25)).toBe('43 1/4');
      expect(formatScore(43.5)).toBe('43 1/2');
      expect(formatScore(43.75)).toBe('43 3/4');
    });

    it('should categorize scores correctly', () => {
      expect(getScoreCategory(43.75)).toBe('perfect');
      expect(getScoreCategory(43.5)).toBe('excellent');
      expect(getScoreCategory(43.25)).toBe('very-good');
      expect(getScoreCategory(43)).toBe('good');
      expect(getScoreCategory(42.5)).toBe('average');
    });
  });
});
