export interface ScoringCriteria {
  suaraDepan: number;
  suaraTengah: number;
  suaraUjung: number;
  irama: number;
  mutuSuara: number;
}

export interface Participant {
  id: string;
  name: string;
  ringNumber: string;
  owner: string;
}

export interface ExtendedParticipant {
  id: string;
  name: string;
  ringNumber: string;
  birdName: string;
  owner: string;
  phone: string;
  email: string;
  category: 'Senior' | 'Junior';
  competitions: number;
  bestScore: number;
  status: 'active' | 'inactive';
}

export interface Score {
  participantId: string;
  round: number;
  criteria: ScoringCriteria;
  total: number;
  judgeId: string;
  timestamp: Date;
}

export interface Competition {
  id: string;
  name: string;
  date: Date;
  block: number;
  participants: Participant[];
  currentRound: number;
  maxRounds: number;
}

export interface Judge {
  id: string;
  name: string;
  initials: string;
}

export const ALLOWED_VALUES = {
  suaraDepan: [9, 8.75, 8.5],
  suaraTengah: [9, 8.75, 8.5],
  suaraUjung: [9, 8.75, 8.5],
  irama: [9, 8.75, 8.5, 8],
  mutuSuara: [9, 8.75, 8.5, 8]
} as const;

export const CRITERIA_LABELS = {
  suaraDepan: 'Suara Depan',
  suaraTengah: 'Suara Tengah',
  suaraUjung: 'Suara Ujung',
  irama: 'Irama',
  mutuSuara: 'Mutu Suara'
} as const;