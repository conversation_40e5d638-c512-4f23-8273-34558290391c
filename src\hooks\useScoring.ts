import { useState, useCallback } from 'react';
import { Competition, Participant, Score, ScoringCriteria, Judge } from '@/types/scoring';
import { calculateTotal } from '@/utils/scoring';

interface UseScoringReturn {
  competition: Competition;
  judge: Judge;
  scores: Score[];
  addScore: (participantId: string, criteria: ScoringCriteria) => void;
  updateScore: (scoreId: string, criteria: ScoringCriteria) => void;
  getParticipantScores: (participantId: string) => Score[];
  getCurrentRoundScore: (participantId: string) => Score | undefined;
  setCurrentRound: (round: number) => void;
}

// Mock data for demo
const mockJudge: Judge = {
  id: 'judge1',
  name: '<PERSON>',
  initials: 'AS'
};

const mockParticipants: Participant[] = [
  { id: '1', name: '<PERSON><PERSON><PERSON>', ringNumber: 'P001', owner: '<PERSON><PERSON>' },
  { id: '2', name: '<PERSON><PERSON>', ringNumber: 'P002', owner: '<PERSON><PERSON>' },
  { id: '3', name: '<PERSON>', ringNumber: 'P003', owner: '<PERSON><PERSON>' },
  { id: '4', name: '<PERSON>', ringNum<PERSON>: 'P004', owner: 'Rina Kartika' },
  { id: '5', name: 'Harmoni Mas', ringNumber: 'P005', owner: 'Dedi Kurniawan' },
  { id: '6', name: 'Suara Merdu', ringNumber: 'P006', owner: 'Lina Sari' },
];

const mockCompetition: Competition = {
  id: 'comp1',
  name: 'Lomba Perkutut P3SI Regional Jawa Barat 2024',
  date: new Date(),
  block: 1,
  participants: mockParticipants,
  currentRound: 1,
  maxRounds: 4
};

export function useScoring(): UseScoringReturn {
  const [competition, setCompetition] = useState<Competition>(mockCompetition);
  const [scores, setScores] = useState<Score[]>([]);

  const addScore = useCallback((participantId: string, criteria: ScoringCriteria) => {
    const newScore: Score = {
      participantId,
      round: competition.currentRound,
      criteria,
      total: calculateTotal(criteria),
      judgeId: mockJudge.id,
      timestamp: new Date()
    };

    setScores(prev => {
      const filtered = prev.filter(
        s => !(s.participantId === participantId && s.round === competition.currentRound)
      );
      return [...filtered, newScore];
    });
  }, [competition.currentRound]);

  const updateScore = useCallback((scoreId: string, criteria: ScoringCriteria) => {
    setScores(prev => prev.map(score => 
      score.participantId === scoreId && score.round === competition.currentRound
        ? { ...score, criteria, total: calculateTotal(criteria), timestamp: new Date() }
        : score
    ));
  }, [competition.currentRound]);

  const getParticipantScores = useCallback((participantId: string) => {
    return scores.filter(s => s.participantId === participantId).sort((a, b) => a.round - b.round);
  }, [scores]);

  const getCurrentRoundScore = useCallback((participantId: string) => {
    return scores.find(s => s.participantId === participantId && s.round === competition.currentRound);
  }, [scores, competition.currentRound]);

  const setCurrentRound = useCallback((round: number) => {
    setCompetition(prev => ({ ...prev, currentRound: round }));
  }, []);

  return {
    competition,
    judge: mockJudge,
    scores,
    addScore,
    updateScore,
    getParticipantScores,
    getCurrentRoundScore,
    setCurrentRound
  };
}