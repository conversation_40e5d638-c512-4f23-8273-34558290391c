import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import {
  ArrowLeft,
  Trophy,
  Calendar as CalendarIcon,
  MapPin,
  Users,
  DollarSign,
  Save,
  X,
  AlertCircle,
  Plus,
  Trash2,
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { ExtendedCompetition, Prize, ContactInfo } from '@/types/scoring';

interface FormData {
  name: string;
  description: string;
  date: Date;
  endDate: Date | null;
  location: string;
  venue: string;
  organizer: string;
  category: 'Regional' | 'National' | 'Open' | 'Championship' | '';
  maxParticipants: number;
  registrationDeadline: Date;
  entryFee: number;
  currency: string;
  maxRounds: number;
  rules: string;
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  contactWebsite: string;
}

interface FormErrors {
  [key: string]: string;
}

export default function CreateCompetition() {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    date: new Date(),
    endDate: null,
    location: '',
    venue: '',
    organizer: '',
    category: '',
    maxParticipants: 100,
    registrationDeadline: new Date(),
    entryFee: 0,
    currency: 'IDR',
    maxRounds: 3,
    rules: '',
    contactName: '',
    contactPhone: '',
    contactEmail: '',
    contactWebsite: '',
  });

  const [prizes, setPrizes] = useState<Prize[]>([
    { id: '1', position: 1, title: 'Champion', amount: 0, currency: 'IDR' },
    { id: '2', position: 2, title: 'Runner-up', amount: 0, currency: 'IDR' },
    { id: '3', position: 3, title: 'Third Place', amount: 0, currency: 'IDR' },
  ]);

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDateOpen, setIsDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);
  const [isRegDeadlineOpen, setIsRegDeadlineOpen] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required fields validation
    if (!formData.name.trim()) {
      newErrors.name = 'Competition name is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (!formData.venue.trim()) {
      newErrors.venue = 'Venue is required';
    }

    if (!formData.organizer.trim()) {
      newErrors.organizer = 'Organizer is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (formData.maxParticipants < 1) {
      newErrors.maxParticipants = 'Max participants must be at least 1';
    }

    if (formData.maxRounds < 1 || formData.maxRounds > 10) {
      newErrors.maxRounds = 'Max rounds must be between 1 and 10';
    }

    if (formData.entryFee < 0) {
      newErrors.entryFee = 'Entry fee cannot be negative';
    }

    if (!formData.contactName.trim()) {
      newErrors.contactName = 'Contact name is required';
    }

    if (!formData.contactPhone.trim()) {
      newErrors.contactPhone = 'Contact phone is required';
    } else if (!/^(\+62|62|0)[0-9]{9,12}$/.test(formData.contactPhone.trim())) {
      newErrors.contactPhone = 'Please enter a valid Indonesian phone number';
    }

    if (!formData.contactEmail.trim()) {
      newErrors.contactEmail = 'Contact email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.contactEmail.trim())) {
      newErrors.contactEmail = 'Please enter a valid email address';
    }

    // Date validations
    if (formData.registrationDeadline >= formData.date) {
      newErrors.registrationDeadline = 'Registration deadline must be before competition date';
    }

    if (formData.endDate && formData.endDate < formData.date) {
      newErrors.endDate = 'End date must be after start date';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePrizeChange = (id: string, field: keyof Prize, value: any) => {
    setPrizes(prev => prev.map(prize => 
      prize.id === id ? { ...prize, [field]: value } : prize
    ));
  };

  const addPrize = () => {
    const newPrize: Prize = {
      id: Date.now().toString(),
      position: prizes.length + 1,
      title: `Position ${prizes.length + 1}`,
      amount: 0,
      currency: 'IDR',
    };
    setPrizes(prev => [...prev, newPrize]);
  };

  const removePrize = (id: string) => {
    setPrizes(prev => prev.filter(prize => prize.id !== id));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      const contactInfo: ContactInfo = {
        name: formData.contactName.trim(),
        phone: formData.contactPhone.trim(),
        email: formData.contactEmail.trim().toLowerCase(),
        website: formData.contactWebsite.trim() || undefined,
      };

      const newCompetition: Omit<ExtendedCompetition, 'id' | 'createdAt' | 'updatedAt' | 'createdBy' | 'participants' | 'judges'> = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        date: formData.date,
        endDate: formData.endDate || undefined,
        location: formData.location.trim(),
        venue: formData.venue.trim(),
        organizer: formData.organizer.trim(),
        category: formData.category as 'Regional' | 'National' | 'Open' | 'Championship',
        status: 'draft',
        maxParticipants: formData.maxParticipants,
        registrationDeadline: formData.registrationDeadline,
        entryFee: formData.entryFee,
        currency: formData.currency,
        currentRound: 0,
        maxRounds: formData.maxRounds,
        rules: formData.rules.trim(),
        prizes: prizes.filter(prize => prize.amount > 0),
        contactInfo,
      };

      // In real app, this would be an API call
      console.log('New competition:', newCompetition);

      toast({
        title: "Success!",
        description: `Competition "${formData.name}" has been created successfully`,
        variant: "default",
      });

      // Navigate back to competitions list
      navigate('/competitions');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create competition. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Check if form has any data
    const hasData = Object.values(formData).some(value => 
      typeof value === 'string' ? value.trim() !== '' : 
      typeof value === 'number' ? value !== 0 : 
      value !== null
    );
    
    if (hasData) {
      const confirmed = window.confirm(
        'You have unsaved changes. Are you sure you want to leave this page?'
      );
      if (!confirmed) return;
    }
    
    navigate('/competitions');
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create New Competition</h1>
          <p className="text-muted-foreground">
            Set up a new bird competition with all necessary details
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Form */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5" />
                  Basic Information
                </CardTitle>
                <CardDescription>
                  Enter the basic details of the competition
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Competition Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Enter competition name"
                    className={errors.name ? 'border-destructive' : ''}
                  />
                  {errors.name && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Enter competition description"
                    rows={3}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="organizer">Organizer *</Label>
                    <Input
                      id="organizer"
                      value={formData.organizer}
                      onChange={(e) => handleInputChange('organizer', e.target.value)}
                      placeholder="Enter organizer name"
                      className={errors.organizer ? 'border-destructive' : ''}
                    />
                    {errors.organizer && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.organizer}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="category">Category *</Label>
                    <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                      <SelectTrigger className={errors.category ? 'border-destructive' : ''}>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Regional">Regional</SelectItem>
                        <SelectItem value="National">National</SelectItem>
                        <SelectItem value="Open">Open</SelectItem>
                        <SelectItem value="Championship">Championship</SelectItem>
                      </SelectContent>
                    </Select>
                    {errors.category && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.category}
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Date & Location */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CalendarIcon className="h-5 w-5" />
                  Date & Location
                </CardTitle>
                <CardDescription>
                  Set the competition schedule and venue
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Competition Date *</Label>
                    <Popover open={isDateOpen} onOpenChange={setIsDateOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !formData.date && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.date ? format(formData.date, "PPP") : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.date}
                          onSelect={(date) => {
                            if (date) {
                              handleInputChange('date', date);
                              setIsDateOpen(false);
                            }
                          }}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <div className="space-y-2">
                    <Label>End Date (Optional)</Label>
                    <Popover open={isEndDateOpen} onOpenChange={setIsEndDateOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !formData.endDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {formData.endDate ? format(formData.endDate, "PPP") : "Pick end date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={formData.endDate || undefined}
                          onSelect={(date) => {
                            handleInputChange('endDate', date);
                            setIsEndDateOpen(false);
                          }}
                          disabled={(date) => date < formData.date}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    {errors.endDate && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.endDate}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="location">Location *</Label>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      placeholder="Enter city/region"
                      className={errors.location ? 'border-destructive' : ''}
                    />
                    {errors.location && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.location}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="venue">Venue *</Label>
                    <Input
                      id="venue"
                      value={formData.venue}
                      onChange={(e) => handleInputChange('venue', e.target.value)}
                      placeholder="Enter venue name"
                      className={errors.venue ? 'border-destructive' : ''}
                    />
                    {errors.venue && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.venue}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Registration Deadline *</Label>
                  <Popover open={isRegDeadlineOpen} onOpenChange={setIsRegDeadlineOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.registrationDeadline && "text-muted-foreground",
                          errors.registrationDeadline && "border-destructive"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.registrationDeadline ? format(formData.registrationDeadline, "PPP") : "Pick deadline"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={formData.registrationDeadline}
                        onSelect={(date) => {
                          if (date) {
                            handleInputChange('registrationDeadline', date);
                            setIsRegDeadlineOpen(false);
                          }
                        }}
                        disabled={(date) => date < new Date() || date >= formData.date}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  {errors.registrationDeadline && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.registrationDeadline}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Competition Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Competition Settings
                </CardTitle>
                <CardDescription>
                  Configure competition parameters and rules
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="space-y-2">
                    <Label htmlFor="maxParticipants">Max Participants *</Label>
                    <Input
                      id="maxParticipants"
                      type="number"
                      min="1"
                      value={formData.maxParticipants}
                      onChange={(e) => handleInputChange('maxParticipants', parseInt(e.target.value) || 0)}
                      className={errors.maxParticipants ? 'border-destructive' : ''}
                    />
                    {errors.maxParticipants && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.maxParticipants}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxRounds">Max Rounds *</Label>
                    <Input
                      id="maxRounds"
                      type="number"
                      min="1"
                      max="10"
                      value={formData.maxRounds}
                      onChange={(e) => handleInputChange('maxRounds', parseInt(e.target.value) || 0)}
                      className={errors.maxRounds ? 'border-destructive' : ''}
                    />
                    {errors.maxRounds && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.maxRounds}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="entryFee">Entry Fee</Label>
                    <div className="flex gap-2">
                      <Input
                        id="entryFee"
                        type="number"
                        min="0"
                        value={formData.entryFee}
                        onChange={(e) => handleInputChange('entryFee', parseInt(e.target.value) || 0)}
                        className={errors.entryFee ? 'border-destructive' : ''}
                      />
                      <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                        <SelectTrigger className="w-20">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="IDR">IDR</SelectItem>
                          <SelectItem value="USD">USD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    {errors.entryFee && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.entryFee}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="rules">Competition Rules</Label>
                  <Textarea
                    id="rules"
                    value={formData.rules}
                    onChange={(e) => handleInputChange('rules', e.target.value)}
                    placeholder="Enter competition rules and regulations"
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Prizes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Prizes
                </CardTitle>
                <CardDescription>
                  Set up prize structure for the competition
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {prizes.map((prize, index) => (
                  <div key={prize.id} className="flex gap-2 items-end">
                    <div className="flex-1 space-y-2">
                      <Label>Position {prize.position}</Label>
                      <Input
                        value={prize.title}
                        onChange={(e) => handlePrizeChange(prize.id, 'title', e.target.value)}
                        placeholder="Prize title"
                      />
                    </div>
                    <div className="flex-1 space-y-2">
                      <Label>Amount</Label>
                      <div className="flex gap-2">
                        <Input
                          type="number"
                          min="0"
                          value={prize.amount}
                          onChange={(e) => handlePrizeChange(prize.id, 'amount', parseInt(e.target.value) || 0)}
                          placeholder="0"
                        />
                        <Select value={prize.currency} onValueChange={(value) => handlePrizeChange(prize.id, 'currency', value)}>
                          <SelectTrigger className="w-20">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="IDR">IDR</SelectItem>
                            <SelectItem value="USD">USD</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    {prizes.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="icon"
                        onClick={() => removePrize(prize.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addPrize}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Prize
                </Button>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Contact Information
                </CardTitle>
                <CardDescription>
                  Provide contact details for participants
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="contactName">Contact Name *</Label>
                    <Input
                      id="contactName"
                      value={formData.contactName}
                      onChange={(e) => handleInputChange('contactName', e.target.value)}
                      placeholder="Enter contact person name"
                      className={errors.contactName ? 'border-destructive' : ''}
                    />
                    {errors.contactName && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.contactName}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactPhone">Contact Phone *</Label>
                    <Input
                      id="contactPhone"
                      value={formData.contactPhone}
                      onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                      placeholder="+62 812-3456-7890"
                      className={errors.contactPhone ? 'border-destructive' : ''}
                    />
                    {errors.contactPhone && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.contactPhone}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="contactEmail">Contact Email *</Label>
                    <Input
                      id="contactEmail"
                      type="email"
                      value={formData.contactEmail}
                      onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                      placeholder="<EMAIL>"
                      className={errors.contactEmail ? 'border-destructive' : ''}
                    />
                    {errors.contactEmail && (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {errors.contactEmail}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contactWebsite">Website (Optional)</Label>
                    <Input
                      id="contactWebsite"
                      value={formData.contactWebsite}
                      onChange={(e) => handleInputChange('contactWebsite', e.target.value)}
                      placeholder="https://example.com"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex gap-3 pt-6">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Creating Competition...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Create Competition
                  </>
                )}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isSubmitting}
                className="flex-1"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          </form>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Preview Card */}
          {formData.name && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Preview</CardTitle>
                <CardDescription>
                  Competition overview
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h3 className="font-medium">{formData.name}</h3>
                  {formData.category && (
                    <Badge variant="outline" className="mt-1">
                      {formData.category}
                    </Badge>
                  )}
                </div>
                {formData.location && (
                  <div className="flex items-center gap-2 text-sm">
                    <MapPin className="h-3 w-3" />
                    {formData.location}
                  </div>
                )}
                {formData.date && (
                  <div className="flex items-center gap-2 text-sm">
                    <CalendarIcon className="h-3 w-3" />
                    {format(formData.date, "PPP")}
                  </div>
                )}
                {formData.organizer && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className="h-3 w-3" />
                    {formData.organizer}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Guidelines */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Competition Guidelines</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Required Information</h4>
                <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                  <li>• Competition name and description</li>
                  <li>• Date, location, and venue</li>
                  <li>• Organizer and category</li>
                  <li>• Contact information</li>
                  <li>• Registration deadline</li>
                </ul>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium">Categories</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="default">Regional</Badge>
                    <span className="text-sm text-muted-foreground">Local competitions</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">National</Badge>
                    <span className="text-sm text-muted-foreground">National level</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Open</Badge>
                    <span className="text-sm text-muted-foreground">Open to all</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className="bg-gradient-to-r from-yellow-400 to-yellow-600">Championship</Badge>
                    <span className="text-sm text-muted-foreground">Championship level</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
